{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information", "VannaDotNet": "Debug"}, "EnableSensitiveDataLogging": true, "EnableDetailedErrors": true}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=vannadotnet_dev;Username=postgres;Password=password;Port=5432"}, "Cors": {"AllowedOrigins": ["*"]}, "RateLimit": {"PermitLimit": 1000, "WindowMinutes": 1, "QueueLimit": 100}, "OpenAI": {"ApiKey": "sk-development-key-here", "DefaultModel": "gpt-3.5-turbo", "DefaultTemperature": 0.1, "DefaultMaxTokens": 2000}, "OpenAIEmbedding": {"ApiKey": "sk-development-key-here", "Model": "text-embedding-ada-002"}}