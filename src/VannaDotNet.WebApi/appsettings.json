{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": false}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=vannadotnet;Username=postgres;Password=password;Port=5432"}, "Jwt": {"SecretKey": "your-super-secret-jwt-key-that-should-be-at-least-32-characters-long", "Issuer": "VannaDotNet", "Audience": "VannaDotNet-Users", "ExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "https://localhost:7000"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"], "AllowCredentials": true}, "RateLimit": {"PermitLimit": 100, "WindowMinutes": 1, "QueueLimit": 10}, "LLM": {"Provider": "openai"}, "OpenAI": {"ApiKey": "your-openai-api-key-here", "DefaultModel": "gpt-4", "DefaultTemperature": 0.1, "DefaultMaxTokens": 4000, "TimeoutSeconds": 60, "MaxRetries": 3}, "Embedding": {"Provider": "openai"}, "OpenAIEmbedding": {"ApiKey": "your-openai-api-key-here", "Model": "text-embedding-ada-002", "EmbeddingDimension": 1536, "MaxTextLength": 8191, "TimeoutSeconds": 60, "MaxRetries": 3}, "VectorStore": {"Provider": "inmemory"}}