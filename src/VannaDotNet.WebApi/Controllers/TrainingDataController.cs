using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VannaDotNet.Application.Features.Training.Commands.AddDdlTrainingData;
using VannaDotNet.Application.Features.Training.Commands.AddDocumentationTrainingData;
using VannaDotNet.Application.Features.Training.Queries.GetTrainingData;
using VannaDotNet.Application.Common.Models;

namespace VannaDotNet.WebApi.Controllers;

/// <summary>
/// Training data management endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
public class TrainingDataController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TrainingDataController> _logger;

    public TrainingDataController(IMediator mediator, ILogger<TrainingDataController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Add DDL (Data Definition Language) training data
    /// </summary>
    /// <param name="command">DDL training data to add</param>
    /// <returns>Created training data</returns>
    [HttpPost("ddl")]
    [ProducesResponseType(typeof(TrainingDataResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TrainingDataResponse>> AddDdl([FromBody] AddDdlTrainingDataCommand command)
    {
        _logger.LogInformation("Adding DDL training data");
        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
    }

    /// <summary>
    /// Add documentation training data
    /// </summary>
    /// <param name="command">Documentation training data to add</param>
    /// <returns>Created training data</returns>
    [HttpPost("documentation")]
    [ProducesResponseType(typeof(TrainingDataResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TrainingDataResponse>> AddDocumentation([FromBody] AddDocumentationTrainingDataCommand command)
    {
        _logger.LogInformation("Adding documentation training data");
        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
    }

    /// <summary>
    /// Add question-SQL pair training data (placeholder - not yet implemented)
    /// </summary>
    /// <param name="request">Question-SQL training data to add</param>
    /// <returns>Created training data</returns>
    [HttpPost("question-sql")]
    [ProducesResponseType(typeof(TrainingDataResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status501NotImplemented)]
    public ActionResult<TrainingDataResponse> AddQuestionSql([FromBody] AddQuestionSqlRequest request)
    {
        _logger.LogInformation("Adding question-SQL training data (not yet implemented)");
        return StatusCode(StatusCodes.Status501NotImplemented, "Question-SQL training data feature not yet implemented");
    }

    /// <summary>
    /// Get training data with pagination and filtering
    /// </summary>
    /// <param name="query">Query parameters for filtering and pagination</param>
    /// <returns>Paginated list of training data</returns>
    [HttpGet]
    [ProducesResponseType(typeof(PagedTrainingDataResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<PagedTrainingDataResult>> Get([FromQuery] GetTrainingDataQuery query)
    {
        _logger.LogInformation("Getting training data with pagination");
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get training data by ID (placeholder - not yet implemented)
    /// </summary>
    /// <param name="id">Training data ID</param>
    /// <returns>Training data details</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(TrainingDataDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status501NotImplemented)]
    public ActionResult<TrainingDataDto> GetById(Guid id)
    {
        _logger.LogInformation("Getting training data by ID: {Id} (not yet implemented)", id);
        return StatusCode(StatusCodes.Status501NotImplemented, "Get training data by ID feature not yet implemented");
    }

    /// <summary>
    /// Get training data by type
    /// </summary>
    /// <param name="type">Training data type (ddl, documentation, question-sql)</param>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 20)</param>
    /// <returns>Filtered training data</returns>
    [HttpGet("type/{type}")]
    [ProducesResponseType(typeof(PagedTrainingDataResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<PagedTrainingDataResult>> GetByType(
        string type,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        if (!Enum.TryParse<Domain.Enums.TrainingDataType>(type, true, out var trainingDataType))
        {
            return BadRequest($"Invalid training data type: {type}");
        }

        _logger.LogInformation("Getting training data by type: {Type}", type);
        var query = new GetTrainingDataQuery(
            Page: page,
            PageSize: pageSize,
            Type: trainingDataType
        );
        var result = await _mediator.Send(query);
        return Ok(result);
    }
}

/// <summary>
/// Request model for adding question-SQL training data
/// </summary>
public class AddQuestionSqlRequest
{
    public string Question { get; set; } = string.Empty;
    public string SqlQuery { get; set; } = string.Empty;
    public string? Description { get; set; }
    public List<string>? Tags { get; set; }
    public string CreatedBy { get; set; } = "system";
}
