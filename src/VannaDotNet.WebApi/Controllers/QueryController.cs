using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VannaDotNet.Application.Features.Query.Commands.GenerateSql;
using VannaDotNet.Application.Common.Models;

namespace VannaDotNet.WebApi.Controllers;

/// <summary>
/// Query processing and SQL generation endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
public class QueryController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<QueryController> _logger;

    public QueryController(IMediator mediator, ILogger<QueryController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Ask a natural language question and get SQL + results (placeholder - not yet implemented)
    /// </summary>
    /// <param name="request">Question and database connection details</param>
    /// <returns>Generated SQL, execution results, and explanation</returns>
    [HttpPost("ask")]
    [ProducesResponseType(typeof(GenerateSqlResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status501NotImplemented)]
    public ActionResult<GenerateSqlResponse> AskQuestion([FromBody] AskQuestionRequest request)
    {
        _logger.LogInformation("Processing question: {Question} (not yet implemented)", request.Question);
        return StatusCode(StatusCodes.Status501NotImplemented, "Ask question feature not yet implemented");
    }

    /// <summary>
    /// Generate SQL from natural language question
    /// </summary>
    /// <param name="command">Question and database connection details</param>
    /// <returns>Generated SQL query with confidence score</returns>
    [HttpPost("generate-sql")]
    [ProducesResponseType(typeof(GenerateSqlResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<GenerateSqlResponse>> GenerateSql([FromBody] GenerateSqlCommand command)
    {
        _logger.LogInformation("Generating SQL for question: {Question}", command.Question);
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Execute SQL query (placeholder - not yet implemented)
    /// </summary>
    /// <param name="request">SQL query and database connection details</param>
    /// <returns>Query execution results</returns>
    [HttpPost("execute-sql")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status501NotImplemented)]
    public ActionResult<object> ExecuteSql([FromBody] ExecuteSqlRequest request)
    {
        _logger.LogInformation("Executing SQL query (not yet implemented)");
        return StatusCode(StatusCodes.Status501NotImplemented, "Execute SQL feature not yet implemented");
    }

}

/// <summary>
/// Request model for asking a question
/// </summary>
public class AskQuestionRequest
{
    public string Question { get; set; } = string.Empty;
    public Guid DatabaseConnectionId { get; set; }
    public GenerationOptions? Options { get; set; }
    public string CreatedBy { get; set; } = "system";
}

/// <summary>
/// Request model for executing SQL
/// </summary>
public class ExecuteSqlRequest
{
    public string SqlQuery { get; set; } = string.Empty;
    public Guid DatabaseConnectionId { get; set; }
    public string CreatedBy { get; set; } = "system";
}

/// <summary>
/// Request model for rating a query session
/// </summary>
public class RateSessionRequest
{
    /// <summary>
    /// Rating from 1 to 5
    /// </summary>
    public int Rating { get; set; }

    /// <summary>
    /// Optional comments about the query session
    /// </summary>
    public string? Comments { get; set; }
}
