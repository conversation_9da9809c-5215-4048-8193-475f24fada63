using System;

namespace VannaDotNet.Domain.Exceptions;

/// <summary>
/// Base exception for all domain-related errors
/// </summary>
public abstract class DomainException : Exception
{
    protected DomainException(string message) : base(message) { }
    
    protected DomainException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when domain validation rules are violated
/// </summary>
public class ValidationException : DomainException
{
    public string PropertyName { get; }
    
    public ValidationException(string propertyName, string message) : base(message)
    {
        PropertyName = propertyName;
    }
    
    public ValidationException(string propertyName, string message, Exception innerException) : base(message, innerException)
    {
        PropertyName = propertyName;
    }
}

/// <summary>
/// Exception thrown when security violations are detected
/// </summary>
public class SecurityException : DomainException
{
    public SecurityException(string message) : base(message) { }
    
    public SecurityException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Exception thrown when business rules are violated
/// </summary>
public class BusinessRuleException : DomainException
{
    public string RuleName { get; }
    
    public BusinessRuleException(string ruleName, string message) : base(message)
    {
        RuleName = ruleName;
    }
    
    public BusinessRuleException(string ruleName, string message, Exception innerException) : base(message, innerException)
    {
        RuleName = ruleName;
    }
}

/// <summary>
/// Exception thrown when an entity is not found
/// </summary>
public class EntityNotFoundException : DomainException
{
    public string EntityType { get; }
    public object EntityId { get; }
    
    public EntityNotFoundException(string entityType, object entityId) 
        : base($"{entityType} with ID '{entityId}' was not found")
    {
        EntityType = entityType;
        EntityId = entityId;
    }
}

/// <summary>
/// Exception thrown when attempting to create a duplicate entity
/// </summary>
public class DuplicateEntityException : DomainException
{
    public string EntityType { get; }
    public string PropertyName { get; }
    public object PropertyValue { get; }
    
    public DuplicateEntityException(string entityType, string propertyName, object propertyValue)
        : base($"{entityType} with {propertyName} '{propertyValue}' already exists")
    {
        EntityType = entityType;
        PropertyName = propertyName;
        PropertyValue = propertyValue;
    }
}

/// <summary>
/// Exception thrown when an operation is not allowed in the current state
/// </summary>
public class InvalidStateOperationException : DomainException
{
    public string Operation { get; }
    public string CurrentState { get; }

    public InvalidStateOperationException(string operation, string currentState)
        : base($"Operation '{operation}' is not allowed in state '{currentState}'")
    {
        Operation = operation;
        CurrentState = currentState;
    }
}

/// <summary>
/// Exception thrown when configuration is invalid or missing
/// </summary>
public class ConfigurationException : DomainException
{
    public string ConfigurationKey { get; }
    
    public ConfigurationException(string configurationKey, string message) : base(message)
    {
        ConfigurationKey = configurationKey;
    }
    
    public ConfigurationException(string configurationKey, string message, Exception innerException) : base(message, innerException)
    {
        ConfigurationKey = configurationKey;
    }
}

/// <summary>
/// Exception thrown when external service operations fail
/// </summary>
public class ExternalServiceException : DomainException
{
    public string ServiceName { get; }
    public string Operation { get; }
    
    public ExternalServiceException(string serviceName, string operation, string message) : base(message)
    {
        ServiceName = serviceName;
        Operation = operation;
    }
    
    public ExternalServiceException(string serviceName, string operation, string message, Exception innerException) : base(message, innerException)
    {
        ServiceName = serviceName;
        Operation = operation;
    }
}

/// <summary>
/// Exception thrown when LLM operations fail
/// </summary>
public class LlmException : ExternalServiceException
{
    public LlmException(string operation, string message) : base("LLM", operation, message) { }
    
    public LlmException(string operation, string message, Exception innerException) : base("LLM", operation, message, innerException) { }
}

/// <summary>
/// Exception thrown when vector store operations fail
/// </summary>
public class VectorStoreException : ExternalServiceException
{
    public VectorStoreException(string operation, string message) : base("VectorStore", operation, message) { }
    
    public VectorStoreException(string operation, string message, Exception innerException) : base("VectorStore", operation, message, innerException) { }
}

/// <summary>
/// Exception thrown when database operations fail
/// </summary>
public class DatabaseException : ExternalServiceException
{
    public string? DatabaseName { get; }
    
    public DatabaseException(string operation, string message, string? databaseName = null) : base("Database", operation, message)
    {
        DatabaseName = databaseName;
    }
    
    public DatabaseException(string operation, string message, Exception innerException, string? databaseName = null) : base("Database", operation, message, innerException)
    {
        DatabaseName = databaseName;
    }
}

/// <summary>
/// Exception thrown when SQL query processing fails
/// </summary>
public class SqlProcessingException : DomainException
{
    public string? SqlQuery { get; }
    public string ProcessingStep { get; }
    
    public SqlProcessingException(string processingStep, string message, string? sqlQuery = null) : base(message)
    {
        ProcessingStep = processingStep;
        SqlQuery = sqlQuery;
    }
    
    public SqlProcessingException(string processingStep, string message, Exception innerException, string? sqlQuery = null) : base(message, innerException)
    {
        ProcessingStep = processingStep;
        SqlQuery = sqlQuery;
    }
}
