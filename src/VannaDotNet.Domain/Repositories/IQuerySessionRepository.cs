using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Enums;

namespace VannaDotNet.Domain.Repositories;

/// <summary>
/// Repository interface for QuerySession entity
/// </summary>
public interface IQuerySessionRepository
{
    /// <summary>
    /// Gets a query session by its ID
    /// </summary>
    Task<QuerySession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all query sessions
    /// </summary>
    Task<IEnumerable<QuerySession>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets query sessions by status
    /// </summary>
    Task<IEnumerable<QuerySession>> GetByStatusAsync(QueryStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets query sessions by database connection ID
    /// </summary>
    Task<IEnumerable<QuerySession>> GetByDatabaseConnectionAsync(Guid databaseConnectionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets query sessions created by a specific user
    /// </summary>
    Task<IEnumerable<QuerySession>> GetByUserAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets paginated query sessions
    /// </summary>
    Task<(IEnumerable<QuerySession> Items, int TotalCount)> GetPaginatedAsync(
        int page,
        int pageSize,
        QueryStatus? status = null,
        Guid? databaseConnectionId = null,
        string? userId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches query sessions by question content
    /// </summary>
    Task<IEnumerable<QuerySession>> SearchByQuestionAsync(string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets successful query sessions for training
    /// </summary>
    Task<IEnumerable<QuerySession>> GetSuccessfulForTrainingAsync(
        int limit,
        double minConfidence = 0.7,
        int? minRating = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets query sessions with high user ratings
    /// </summary>
    Task<IEnumerable<QuerySession>> GetHighRatedAsync(int minRating, int limit, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets recent query sessions
    /// </summary>
    Task<IEnumerable<QuerySession>> GetRecentAsync(int limit, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets query sessions with long execution times
    /// </summary>
    Task<IEnumerable<QuerySession>> GetSlowQueriesAsync(TimeSpan minExecutionTime, int limit, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets failed query sessions
    /// </summary>
    Task<IEnumerable<QuerySession>> GetFailedAsync(int limit, DateTime? fromDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets query sessions by date range
    /// </summary>
    Task<IEnumerable<QuerySession>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new query session
    /// </summary>
    Task<QuerySession> AddAsync(QuerySession querySession, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing query session
    /// </summary>
    Task UpdateAsync(QuerySession querySession, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a query session by ID
    /// </summary>
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Soft deletes a query session by ID
    /// </summary>
    Task SoftDeleteAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets query sessions that can be used for training (not already used)
    /// </summary>
    Task<IEnumerable<QuerySession>> GetAvailableForTrainingAsync(
        int limit,
        double minConfidence = 0.8,
        int? minRating = 4,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Marks query sessions as used for training
    /// </summary>
    Task MarkAsUsedForTrainingAsync(IEnumerable<Guid> sessionIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets query session statistics
    /// </summary>
    Task<QuerySessionStatistics> GetStatisticsAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        Guid? databaseConnectionId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance metrics for query sessions
    /// </summary>
    Task<QueryPerformanceMetrics> GetPerformanceMetricsAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets popular questions (most frequently asked)
    /// </summary>
    Task<IEnumerable<PopularQuestion>> GetPopularQuestionsAsync(int limit, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cleans up old query sessions
    /// </summary>
    Task CleanupOldSessionsAsync(DateTime olderThan, CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistics about query sessions
/// </summary>
public class QuerySessionStatistics
{
    public int TotalSessions { get; set; }
    public int SuccessfulSessions { get; set; }
    public int FailedSessions { get; set; }
    public int CancelledSessions { get; set; }
    public double SuccessRate { get; set; }
    public TimeSpan AverageExecutionTime { get; set; }
    public TimeSpan AverageGenerationTime { get; set; }
    public double AverageConfidence { get; set; }
    public double AverageUserRating { get; set; }
    public Dictionary<QueryStatus, int> SessionsByStatus { get; set; } = new();
    public Dictionary<string, int> SessionsByDatabase { get; set; } = new();
    public int SessionsUsedForTraining { get; set; }
}

/// <summary>
/// Performance metrics for query sessions
/// </summary>
public class QueryPerformanceMetrics
{
    public TimeSpan FastestExecution { get; set; }
    public TimeSpan SlowestExecution { get; set; }
    public TimeSpan MedianExecutionTime { get; set; }
    public TimeSpan P95ExecutionTime { get; set; }
    public TimeSpan P99ExecutionTime { get; set; }
    public int TotalRowsReturned { get; set; }
    public double AverageRowsPerQuery { get; set; }
    public int QueriesWithErrors { get; set; }
    public Dictionary<string, int> ErrorsByType { get; set; } = new();
}

/// <summary>
/// Popular question information
/// </summary>
public class PopularQuestion
{
    public string Question { get; set; } = string.Empty;
    public int Count { get; set; }
    public double AverageConfidence { get; set; }
    public double AverageRating { get; set; }
    public DateTime LastAsked { get; set; }
}
