using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Enums;

namespace VannaDotNet.Domain.Repositories;

/// <summary>
/// Repository interface for DatabaseConnection entity
/// </summary>
public interface IDatabaseConnectionRepository
{
    /// <summary>
    /// Gets a database connection by its ID
    /// </summary>
    Task<DatabaseConnection?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all database connections
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets active database connections
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> GetActiveAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets database connections by type
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> GetByTypeAsync(DatabaseType type, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets database connections by environment
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> GetByEnvironmentAsync(string environment, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a database connection by name
    /// </summary>
    Task<DatabaseConnection?> GetByNameAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets paginated database connections
    /// </summary>
    Task<(IEnumerable<DatabaseConnection> Items, int TotalCount)> GetPaginatedAsync(
        int page,
        int pageSize,
        DatabaseType? type = null,
        bool? isActive = null,
        string? environment = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches database connections by name or description
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets database connections that need testing
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> GetNeedingTestAsync(TimeSpan testInterval, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets database connections with failed tests
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> GetWithFailedTestsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets database connections with high usage
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> GetHighUsageAsync(int limit, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets database connections with low success rates
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> GetLowSuccessRateAsync(double maxSuccessRate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new database connection
    /// </summary>
    Task<DatabaseConnection> AddAsync(DatabaseConnection databaseConnection, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing database connection
    /// </summary>
    Task UpdateAsync(DatabaseConnection databaseConnection, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a database connection by ID
    /// </summary>
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Soft deletes a database connection by ID
    /// </summary>
    Task SoftDeleteAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a database connection name already exists
    /// </summary>
    Task<bool> NameExistsAsync(string name, Guid? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets database connection statistics
    /// </summary>
    Task<DatabaseConnectionStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets usage statistics for a specific database connection
    /// </summary>
    Task<DatabaseUsageStatistics> GetUsageStatisticsAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates test results for a database connection
    /// </summary>
    Task UpdateTestResultAsync(Guid id, bool success, string? errorMessage = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Records query execution statistics
    /// </summary>
    Task RecordQueryExecutionAsync(Guid id, bool success, TimeSpan? executionTime = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all unique environments
    /// </summary>
    Task<IEnumerable<string>> GetEnvironmentsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets database connections by host
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> GetByHostAsync(string host, CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk updates connection statuses
    /// </summary>
    Task BulkUpdateStatusAsync(IEnumerable<Guid> ids, bool isActive, string updatedBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets database connections that haven't been used recently
    /// </summary>
    Task<IEnumerable<DatabaseConnection>> GetUnusedAsync(TimeSpan unusedPeriod, CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistics about database connections
/// </summary>
public class DatabaseConnectionStatistics
{
    public int TotalConnections { get; set; }
    public int ActiveConnections { get; set; }
    public int InactiveConnections { get; set; }
    public Dictionary<DatabaseType, int> ConnectionsByType { get; set; } = new();
    public Dictionary<string, int> ConnectionsByEnvironment { get; set; } = new();
    public int ConnectionsWithFailedTests { get; set; }
    public int ConnectionsNeedingTest { get; set; }
    public double AverageSuccessRate { get; set; }
    public TimeSpan AverageExecutionTime { get; set; }
    public long TotalQueries { get; set; }
    public long TotalSuccessfulQueries { get; set; }
    public long TotalFailedQueries { get; set; }
}

/// <summary>
/// Usage statistics for a specific database connection
/// </summary>
public class DatabaseUsageStatistics
{
    public Guid DatabaseConnectionId { get; set; }
    public string DatabaseName { get; set; } = string.Empty;
    public long TotalQueries { get; set; }
    public long SuccessfulQueries { get; set; }
    public long FailedQueries { get; set; }
    public double SuccessRate { get; set; }
    public TimeSpan TotalExecutionTime { get; set; }
    public TimeSpan AverageExecutionTime { get; set; }
    public TimeSpan FastestQuery { get; set; }
    public TimeSpan SlowestQuery { get; set; }
    public DateTime? LastUsed { get; set; }
    public DateTime? LastTested { get; set; }
    public bool? LastTestResult { get; set; }
    public Dictionary<string, int> QueriesByHour { get; set; } = new();
    public Dictionary<string, int> QueriesByDay { get; set; } = new();
    public List<string> MostCommonErrors { get; set; } = new();
}
