using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Enums;

namespace VannaDotNet.Domain.Repositories;

/// <summary>
/// Repository interface for TrainingData entity
/// </summary>
public interface ITrainingDataRepository
{
    /// <summary>
    /// Gets a training data entity by its ID
    /// </summary>
    Task<TrainingData?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all training data entities
    /// </summary>
    Task<IEnumerable<TrainingData>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets training data entities by type
    /// </summary>
    Task<IEnumerable<TrainingData>> GetByTypeAsync(TrainingDataType type, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets active training data entities by type
    /// </summary>
    Task<IEnumerable<TrainingData>> GetActiveByTypeAsync(TrainingDataType type, CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches training data by tags
    /// </summary>
    Task<IEnumerable<TrainingData>> SearchByTagsAsync(IEnumerable<string> tags, CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches training data by content
    /// </summary>
    Task<IEnumerable<TrainingData>> SearchByContentAsync(string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets training data by schema and table name
    /// </summary>
    Task<IEnumerable<TrainingData>> GetBySchemaAndTableAsync(string? schemaName, string? tableName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets paginated training data
    /// </summary>
    Task<(IEnumerable<TrainingData> Items, int TotalCount)> GetPaginatedAsync(
        int page, 
        int pageSize, 
        TrainingDataType? type = null,
        bool? isActive = null,
        IEnumerable<string>? tags = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets training data with highest quality scores
    /// </summary>
    Task<IEnumerable<TrainingData>> GetHighQualityAsync(double minQualityScore, int limit, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets most frequently used training data
    /// </summary>
    Task<IEnumerable<TrainingData>> GetMostUsedAsync(int limit, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets recently added training data
    /// </summary>
    Task<IEnumerable<TrainingData>> GetRecentAsync(int limit, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new training data entity
    /// </summary>
    Task<TrainingData> AddAsync(TrainingData trainingData, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds multiple training data entities
    /// </summary>
    Task<IEnumerable<TrainingData>> AddRangeAsync(IEnumerable<TrainingData> trainingDataList, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing training data entity
    /// </summary>
    Task UpdateAsync(TrainingData trainingData, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates multiple training data entities
    /// </summary>
    Task UpdateRangeAsync(IEnumerable<TrainingData> trainingDataList, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a training data entity by ID (hard delete)
    /// </summary>
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Soft deletes a training data entity by ID
    /// </summary>
    Task SoftDeleteAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Restores a soft-deleted training data entity
    /// </summary>
    Task RestoreAsync(Guid id, string restoredBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if training data with the same content already exists
    /// </summary>
    Task<bool> ExistsAsync(string content, TrainingDataType type, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets count of training data by type
    /// </summary>
    Task<Dictionary<TrainingDataType, int>> GetCountByTypeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all unique tags
    /// </summary>
    Task<IEnumerable<string>> GetAllTagsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all unique schema names
    /// </summary>
    Task<IEnumerable<string>> GetAllSchemaNamesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all unique table names for a schema
    /// </summary>
    Task<IEnumerable<string>> GetTableNamesForSchemaAsync(string schemaName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates usage statistics for training data
    /// </summary>
    Task UpdateUsageAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk updates quality scores
    /// </summary>
    Task UpdateQualityScoresAsync(Dictionary<Guid, double> qualityScores, string updatedBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets training data that hasn't been used recently
    /// </summary>
    Task<IEnumerable<TrainingData>> GetUnusedAsync(TimeSpan unusedPeriod, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets training data statistics
    /// </summary>
    Task<TrainingDataStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistics about training data
/// </summary>
public class TrainingDataStatistics
{
    public int TotalCount { get; set; }
    public int ActiveCount { get; set; }
    public Dictionary<TrainingDataType, int> CountByType { get; set; } = new();
    public double AverageQualityScore { get; set; }
    public int TotalUsageCount { get; set; }
    public DateTime? LastAddedAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public int UniqueTagCount { get; set; }
    public int UniqueSchemaCount { get; set; }
    public int UniqueTableCount { get; set; }
}
