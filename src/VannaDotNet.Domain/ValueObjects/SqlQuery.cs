using System;
using System.Linq;
using System.Text.RegularExpressions;
using VannaDotNet.Domain.Enums;
using VannaDotNet.Domain.Exceptions;

namespace VannaDotNet.Domain.ValueObjects;

/// <summary>
/// Value object representing a SQL query with validation and type detection
/// </summary>
public record SqlQuery
{
    private static readonly string[] DangerousPatterns = 
    {
        ";--", "/*", "*/", "xp_", "sp_", "exec ", "execute ", "drop ", "truncate ", "alter "
    };

    private static readonly Regex SqlCommentRegex = new(@"--.*$|/\*.*?\*/", RegexOptions.Multiline | RegexOptions.Compiled);

    /// <summary>
    /// The SQL query string
    /// </summary>
    public string Query { get; }

    /// <summary>
    /// The type of SQL query
    /// </summary>
    public SqlQueryType Type { get; }

    /// <summary>
    /// Indicates if this is a read-only query
    /// </summary>
    public bool IsReadOnly { get; }

    /// <summary>
    /// Estimated complexity score of the query
    /// </summary>
    public int ComplexityScore { get; }

    /// <summary>
    /// Creates a new SqlQuery instance with validation
    /// </summary>
    /// <param name="query">The SQL query string</param>
    /// <exception cref="ArgumentException">Thrown when query is null or empty</exception>
    /// <exception cref="SecurityException">Thrown when potentially dangerous patterns are detected</exception>
    public SqlQuery(string query)
    {
        if (string.IsNullOrWhiteSpace(query))
            throw new ArgumentException("SQL query cannot be empty", nameof(query));

        Query = NormalizeQuery(query);
        Type = DetermineQueryType(Query);
        IsReadOnly = DetermineIfReadOnly(Type);
        ComplexityScore = CalculateComplexityScore(Query);

        ValidateQuery();
    }

    /// <summary>
    /// Normalizes the SQL query by trimming whitespace and removing comments
    /// </summary>
    private static string NormalizeQuery(string query)
    {
        // Remove SQL comments
        var normalized = SqlCommentRegex.Replace(query, string.Empty);
        
        // Trim and normalize whitespace
        normalized = Regex.Replace(normalized.Trim(), @"\s+", " ");
        
        return normalized;
    }

    /// <summary>
    /// Determines the type of SQL query based on the first keyword
    /// </summary>
    private static SqlQueryType DetermineQueryType(string query)
    {
        var upperQuery = query.ToUpperInvariant().TrimStart();

        return upperQuery switch
        {
            var q when q.StartsWith("SELECT") => SqlQueryType.Select,
            var q when q.StartsWith("WITH") => SqlQueryType.With,
            var q when q.StartsWith("INSERT") => SqlQueryType.Insert,
            var q when q.StartsWith("UPDATE") => SqlQueryType.Update,
            var q when q.StartsWith("DELETE") => SqlQueryType.Delete,
            var q when q.StartsWith("CREATE") => SqlQueryType.Create,
            var q when q.StartsWith("ALTER") => SqlQueryType.Alter,
            var q when q.StartsWith("DROP") => SqlQueryType.Drop,
            _ => SqlQueryType.Unknown
        };
    }

    /// <summary>
    /// Determines if the query is read-only based on its type
    /// </summary>
    private static bool DetermineIfReadOnly(SqlQueryType type)
    {
        return type is SqlQueryType.Select or SqlQueryType.With;
    }

    /// <summary>
    /// Calculates a complexity score for the query based on various factors
    /// </summary>
    private static int CalculateComplexityScore(string query)
    {
        var score = 0;
        var upperQuery = query.ToUpperInvariant();

        // Base score for query length
        score += query.Length / 100;

        // Add points for complex operations
        score += CountOccurrences(upperQuery, "JOIN") * 2;
        score += CountOccurrences(upperQuery, "UNION") * 3;
        score += CountOccurrences(upperQuery, "SUBQUERY") * 3;
        score += CountOccurrences(upperQuery, "CASE") * 2;
        score += CountOccurrences(upperQuery, "GROUP BY") * 2;
        score += CountOccurrences(upperQuery, "ORDER BY") * 1;
        score += CountOccurrences(upperQuery, "HAVING") * 2;
        score += CountOccurrences(upperQuery, "WINDOW") * 3;

        // Add points for aggregate functions
        var aggregateFunctions = new[] { "COUNT", "SUM", "AVG", "MIN", "MAX", "STDDEV" };
        foreach (var func in aggregateFunctions)
        {
            score += CountOccurrences(upperQuery, func) * 1;
        }

        return Math.Max(1, score); // Minimum score of 1
    }

    /// <summary>
    /// Counts occurrences of a substring in a string
    /// </summary>
    private static int CountOccurrences(string text, string pattern)
    {
        var count = 0;
        var index = 0;
        
        while ((index = text.IndexOf(pattern, index, StringComparison.Ordinal)) != -1)
        {
            count++;
            index += pattern.Length;
        }
        
        return count;
    }

    /// <summary>
    /// Validates the query for security and syntax issues
    /// </summary>
    /// <exception cref="SecurityException">Thrown when potentially dangerous patterns are detected</exception>
    private void ValidateQuery()
    {
        // Check for dangerous patterns (basic SQL injection prevention)
        var lowerQuery = Query.ToLowerInvariant();
        
        foreach (var pattern in DangerousPatterns)
        {
            if (lowerQuery.Contains(pattern))
            {
                throw new SecurityException($"Potentially dangerous SQL pattern detected: {pattern}");
            }
        }

        // Additional validation for non-SELECT queries
        if (!IsReadOnly)
        {
            throw new SecurityException("Only SELECT and WITH queries are allowed for security reasons");
        }

        // Check for balanced parentheses
        if (!AreParenthesesBalanced(Query))
        {
            throw new ArgumentException("SQL query has unbalanced parentheses");
        }
    }

    /// <summary>
    /// Checks if parentheses are balanced in the query
    /// </summary>
    private static bool AreParenthesesBalanced(string query)
    {
        var count = 0;
        
        foreach (var ch in query)
        {
            if (ch == '(')
                count++;
            else if (ch == ')')
                count--;
                
            if (count < 0)
                return false;
        }
        
        return count == 0;
    }

    /// <summary>
    /// Extracts table names from the SQL query (basic implementation)
    /// </summary>
    public string[] GetTableNames()
    {
        var tablePattern = @"\bFROM\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b|\bJOIN\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b";
        var matches = Regex.Matches(Query, tablePattern, RegexOptions.IgnoreCase);
        
        return matches
            .SelectMany(m => m.Groups.Cast<Group>().Skip(1).Where(g => g.Success))
            .Select(g => g.Value.Trim())
            .Distinct()
            .ToArray();
    }

    /// <summary>
    /// Returns a formatted version of the SQL query
    /// </summary>
    public string ToFormattedString()
    {
        // Basic SQL formatting - in a real implementation, you might use a proper SQL formatter
        return Query
            .Replace(" SELECT ", "\nSELECT ")
            .Replace(" FROM ", "\nFROM ")
            .Replace(" WHERE ", "\nWHERE ")
            .Replace(" GROUP BY ", "\nGROUP BY ")
            .Replace(" ORDER BY ", "\nORDER BY ")
            .Replace(" HAVING ", "\nHAVING ")
            .Replace(" JOIN ", "\nJOIN ")
            .Replace(" LEFT JOIN ", "\nLEFT JOIN ")
            .Replace(" RIGHT JOIN ", "\nRIGHT JOIN ")
            .Replace(" INNER JOIN ", "\nINNER JOIN ")
            .Replace(" UNION ", "\nUNION ");
    }

    public override string ToString() => Query;

    /// <summary>
    /// Implicit conversion from string to SqlQuery
    /// </summary>
    public static implicit operator SqlQuery(string query) => new(query);

    /// <summary>
    /// Implicit conversion from SqlQuery to string
    /// </summary>
    public static implicit operator string(SqlQuery sqlQuery) => sqlQuery.Query;
}
