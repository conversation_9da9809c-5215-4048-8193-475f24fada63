using System;
using System.Collections.Generic;
using System.Linq;
using VannaDotNet.Domain.Common;
using VannaDotNet.Domain.Enums;
using VannaDotNet.Domain.Exceptions;

namespace VannaDotNet.Domain.Entities;

/// <summary>
/// Represents training data used to improve the AI model's performance
/// </summary>
public class TrainingData : BaseEntity
{
    private readonly List<string> _tags = new();

    /// <summary>
    /// The content of the training data (DDL, documentation, SQL, etc.)
    /// </summary>
    public string Content { get; private set; } = string.Empty;

    /// <summary>
    /// The type of training data
    /// </summary>
    public TrainingDataType Type { get; private set; }

    /// <summary>
    /// Optional description of the training data
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// Tags associated with this training data for categorization and search
    /// </summary>
    public IReadOnlyList<string> Tags => _tags.AsReadOnly();

    /// <summary>
    /// The question associated with this training data (for QuestionSql type)
    /// </summary>
    public string? Question { get; private set; }

    /// <summary>
    /// The SQL query associated with this training data (for QuestionSql type)
    /// </summary>
    public string? SqlQuery { get; private set; }

    /// <summary>
    /// The database schema this training data relates to
    /// </summary>
    public string? SchemaName { get; private set; }

    /// <summary>
    /// The table name this training data relates to
    /// </summary>
    public string? TableName { get; private set; }

    /// <summary>
    /// Indicates if this training data is active and should be used for training
    /// </summary>
    public bool IsActive { get; private set; } = true;

    /// <summary>
    /// Quality score of this training data (0.0 to 1.0)
    /// </summary>
    public double QualityScore { get; private set; } = 1.0;

    /// <summary>
    /// Number of times this training data has been used in query generation
    /// </summary>
    public int UsageCount { get; private set; }

    /// <summary>
    /// Last time this training data was used
    /// </summary>
    public DateTime? LastUsedAt { get; private set; }

    // Private constructor for EF Core
    private TrainingData() { }

    /// <summary>
    /// Creates DDL training data
    /// </summary>
    public static TrainingData CreateDdl(
        string ddl, 
        string? description = null, 
        IEnumerable<string>? tags = null,
        string? schemaName = null,
        string? tableName = null,
        string createdBy = "system")
    {
        ValidateContent(ddl, nameof(ddl));

        var trainingData = new TrainingData
        {
            Content = ddl.Trim(),
            Type = TrainingDataType.Ddl,
            Description = description?.Trim(),
            SchemaName = schemaName?.Trim(),
            TableName = tableName?.Trim()
        };

        trainingData.SetCreatedBy(createdBy);
        trainingData.AddTags(tags);
        trainingData.ExtractMetadataFromDdl();

        return trainingData;
    }

    /// <summary>
    /// Creates documentation training data
    /// </summary>
    public static TrainingData CreateDocumentation(
        string documentation, 
        string? description = null, 
        IEnumerable<string>? tags = null,
        string createdBy = "system")
    {
        ValidateContent(documentation, nameof(documentation));

        var trainingData = new TrainingData
        {
            Content = documentation.Trim(),
            Type = TrainingDataType.Documentation,
            Description = description?.Trim()
        };

        trainingData.SetCreatedBy(createdBy);
        trainingData.AddTags(tags);

        return trainingData;
    }

    /// <summary>
    /// Creates question-SQL pair training data
    /// </summary>
    public static TrainingData CreateQuestionSql(
        string question, 
        string sql, 
        string? description = null, 
        IEnumerable<string>? tags = null,
        string createdBy = "system")
    {
        ValidateContent(question, nameof(question));
        ValidateContent(sql, nameof(sql));

        var trainingData = new TrainingData
        {
            Content = $"Q: {question.Trim()}\nSQL: {sql.Trim()}",
            Type = TrainingDataType.QuestionSql,
            Question = question.Trim(),
            SqlQuery = sql.Trim(),
            Description = description?.Trim()
        };

        trainingData.SetCreatedBy(createdBy);
        trainingData.AddTags(tags);

        return trainingData;
    }

    /// <summary>
    /// Creates schema training data
    /// </summary>
    public static TrainingData CreateSchema(
        string schemaInfo, 
        string schemaName,
        string? description = null, 
        IEnumerable<string>? tags = null,
        string createdBy = "system")
    {
        ValidateContent(schemaInfo, nameof(schemaInfo));
        ValidateContent(schemaName, nameof(schemaName));

        var trainingData = new TrainingData
        {
            Content = schemaInfo.Trim(),
            Type = TrainingDataType.Schema,
            SchemaName = schemaName.Trim(),
            Description = description?.Trim()
        };

        trainingData.SetCreatedBy(createdBy);
        trainingData.AddTags(tags);

        return trainingData;
    }

    /// <summary>
    /// Updates the content of the training data
    /// </summary>
    public void UpdateContent(string newContent, string updatedBy)
    {
        ValidateContent(newContent, nameof(newContent));
        
        Content = newContent.Trim();
        SetUpdated(updatedBy);

        if (Type == TrainingDataType.Ddl)
        {
            ExtractMetadataFromDdl();
        }
    }

    /// <summary>
    /// Updates the description
    /// </summary>
    public void UpdateDescription(string? newDescription, string updatedBy)
    {
        Description = newDescription?.Trim();
        SetUpdated(updatedBy);
    }

    /// <summary>
    /// Adds tags to the training data
    /// </summary>
    public void AddTags(IEnumerable<string>? tags)
    {
        if (tags == null) return;

        foreach (var tag in tags.Where(t => !string.IsNullOrWhiteSpace(t)))
        {
            var normalizedTag = tag.Trim().ToLowerInvariant();
            if (!_tags.Contains(normalizedTag))
            {
                _tags.Add(normalizedTag);
            }
        }
    }

    /// <summary>
    /// Removes a tag from the training data
    /// </summary>
    public void RemoveTag(string tag)
    {
        if (string.IsNullOrWhiteSpace(tag)) return;
        
        var normalizedTag = tag.Trim().ToLowerInvariant();
        _tags.Remove(normalizedTag);
    }

    /// <summary>
    /// Clears all tags
    /// </summary>
    public void ClearTags()
    {
        _tags.Clear();
    }

    /// <summary>
    /// Activates the training data
    /// </summary>
    public void Activate(string updatedBy)
    {
        IsActive = true;
        SetUpdated(updatedBy);
    }

    /// <summary>
    /// Deactivates the training data
    /// </summary>
    public void Deactivate(string updatedBy)
    {
        IsActive = false;
        SetUpdated(updatedBy);
    }

    /// <summary>
    /// Updates the quality score
    /// </summary>
    public void UpdateQualityScore(double score, string updatedBy)
    {
        if (score < 0.0 || score > 1.0)
            throw new ValidationException(nameof(score), "Quality score must be between 0.0 and 1.0");

        QualityScore = score;
        SetUpdated(updatedBy);
    }

    /// <summary>
    /// Records usage of this training data
    /// </summary>
    public void RecordUsage()
    {
        UsageCount++;
        LastUsedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Checks if the training data has a specific tag
    /// </summary>
    public bool HasTag(string tag)
    {
        if (string.IsNullOrWhiteSpace(tag)) return false;
        
        var normalizedTag = tag.Trim().ToLowerInvariant();
        return _tags.Contains(normalizedTag);
    }

    /// <summary>
    /// Validates content is not null or empty
    /// </summary>
    private static void ValidateContent(string content, string parameterName)
    {
        if (string.IsNullOrWhiteSpace(content))
            throw new ValidationException(parameterName, $"{parameterName} cannot be null or empty");

        if (content.Length > 10000)
            throw new ValidationException(parameterName, $"{parameterName} cannot exceed 10,000 characters");
    }

    /// <summary>
    /// Extracts metadata from DDL content
    /// </summary>
    private void ExtractMetadataFromDdl()
    {
        if (Type != TrainingDataType.Ddl) return;

        var upperContent = Content.ToUpperInvariant();
        
        // Extract table name from CREATE TABLE statements
        if (upperContent.Contains("CREATE TABLE"))
        {
            var match = System.Text.RegularExpressions.Regex.Match(
                Content, 
                @"CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?(?:(\w+)\.)?(\w+)",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                
            if (match.Success)
            {
                if (!string.IsNullOrEmpty(match.Groups[1].Value))
                    SchemaName = match.Groups[1].Value;
                    
                TableName = match.Groups[2].Value;
            }
        }

        // Add automatic tags based on DDL content
        if (upperContent.Contains("PRIMARY KEY"))
            AddTags(new[] { "primary_key" });
            
        if (upperContent.Contains("FOREIGN KEY"))
            AddTags(new[] { "foreign_key" });
            
        if (upperContent.Contains("INDEX") || upperContent.Contains("KEY"))
            AddTags(new[] { "index" });
    }
}
