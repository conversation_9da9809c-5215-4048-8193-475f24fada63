using System;
using System.Collections.Generic;
using System.Text.Json;
using VannaDotNet.Domain.Common;
using VannaDotNet.Domain.Enums;
using VannaDotNet.Domain.Exceptions;
using VannaDotNet.Domain.ValueObjects;

namespace VannaDotNet.Domain.Entities;

/// <summary>
/// Represents a query session where a user asks a question and receives SQL and results
/// </summary>
public class QuerySession : BaseEntity
{
    /// <summary>
    /// The natural language question asked by the user
    /// </summary>
    public string Question { get; private set; } = string.Empty;

    /// <summary>
    /// The generated SQL query
    /// </summary>
    public SqlQuery? GeneratedSql { get; private set; }

    /// <summary>
    /// Current status of the query session
    /// </summary>
    public QueryStatus Status { get; private set; } = QueryStatus.Created;

    /// <summary>
    /// The results of the executed query (JSON format)
    /// </summary>
    public string? Results { get; private set; }

    /// <summary>
    /// Confidence score of the generated SQL (0.0 to 1.0)
    /// </summary>
    public double? Confidence { get; private set; }

    /// <summary>
    /// Time taken to execute the query
    /// </summary>
    public TimeSpan? ExecutionTime { get; private set; }

    /// <summary>
    /// ID of the database connection used
    /// </summary>
    public Guid DatabaseConnectionId { get; private set; }

    /// <summary>
    /// Error message if the query failed
    /// </summary>
    public string? ErrorMessage { get; private set; }

    /// <summary>
    /// Stack trace if an error occurred
    /// </summary>
    public string? ErrorStackTrace { get; private set; }

    /// <summary>
    /// Number of rows returned by the query
    /// </summary>
    public int? RowCount { get; private set; }

    /// <summary>
    /// Explanation of the generated SQL
    /// </summary>
    public string? Explanation { get; private set; }

    /// <summary>
    /// Follow-up questions suggested by the AI
    /// </summary>
    public string? FollowUpQuestions { get; private set; }

    /// <summary>
    /// Visualization configuration (JSON format)
    /// </summary>
    public string? VisualizationConfig { get; private set; }

    /// <summary>
    /// User feedback on the query results (1-5 stars)
    /// </summary>
    public int? UserRating { get; private set; }

    /// <summary>
    /// User comments on the query results
    /// </summary>
    public string? UserComments { get; private set; }

    /// <summary>
    /// Indicates if this session was used for training
    /// </summary>
    public bool UsedForTraining { get; private set; }

    /// <summary>
    /// Time when the query generation started
    /// </summary>
    public DateTime? GenerationStartedAt { get; private set; }

    /// <summary>
    /// Time when the query generation completed
    /// </summary>
    public DateTime? GenerationCompletedAt { get; private set; }

    /// <summary>
    /// Time when the query execution started
    /// </summary>
    public DateTime? ExecutionStartedAt { get; private set; }

    /// <summary>
    /// Time when the query execution completed
    /// </summary>
    public DateTime? ExecutionCompletedAt { get; private set; }

    // Private constructor for EF Core
    private QuerySession() { }

    /// <summary>
    /// Creates a new query session
    /// </summary>
    public static QuerySession Create(
        string question, 
        Guid databaseConnectionId, 
        string createdBy = "system")
    {
        if (string.IsNullOrWhiteSpace(question))
            throw new ValidationException(nameof(question), "Question cannot be null or empty");

        if (databaseConnectionId == Guid.Empty)
            throw new ValidationException(nameof(databaseConnectionId), "Database connection ID cannot be empty");

        var session = new QuerySession
        {
            Question = question.Trim(),
            DatabaseConnectionId = databaseConnectionId,
            Status = QueryStatus.Created
        };

        session.SetCreatedBy(createdBy);
        return session;
    }

    /// <summary>
    /// Marks the query generation as started
    /// </summary>
    public void StartGeneration()
    {
        if (Status != QueryStatus.Created)
            throw new InvalidStateOperationException("StartGeneration", Status.ToString());

        Status = QueryStatus.Processing;
        GenerationStartedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Marks the query generation as completed with the generated SQL
    /// </summary>
    public void CompleteGeneration(
        string sql, 
        double confidence, 
        string? explanation = null, 
        IEnumerable<string>? followUpQuestions = null)
    {
        if (Status != QueryStatus.Processing)
            throw new InvalidStateOperationException("CompleteGeneration", Status.ToString());

        if (string.IsNullOrWhiteSpace(sql))
            throw new ValidationException(nameof(sql), "Generated SQL cannot be null or empty");

        if (confidence < 0.0 || confidence > 1.0)
            throw new ValidationException(nameof(confidence), "Confidence must be between 0.0 and 1.0");

        GeneratedSql = new SqlQuery(sql);
        Confidence = confidence;
        Explanation = explanation?.Trim();
        
        if (followUpQuestions != null)
        {
            FollowUpQuestions = JsonSerializer.Serialize(followUpQuestions);
        }

        Status = QueryStatus.Generated;
        GenerationCompletedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Validates the generated SQL
    /// </summary>
    public void ValidateSql()
    {
        if (Status != QueryStatus.Generated)
            throw new InvalidStateOperationException("ValidateSql", Status.ToString());

        if (GeneratedSql == null)
            throw new InvalidStateOperationException("ValidateSql", "No SQL has been generated");

        // Additional validation logic can be added here
        Status = QueryStatus.Validated;
    }

    /// <summary>
    /// Marks the query execution as started
    /// </summary>
    public void StartExecution()
    {
        if (Status != QueryStatus.Validated && Status != QueryStatus.Generated)
            throw new InvalidStateOperationException("StartExecution", Status.ToString());

        ExecutionStartedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Marks the query execution as completed successfully
    /// </summary>
    public void CompleteExecution(
        object results, 
        int rowCount, 
        string? visualizationConfig = null)
    {
        if (ExecutionStartedAt == null)
            throw new InvalidStateOperationException("CompleteExecution", "Execution was not started");

        Results = JsonSerializer.Serialize(results);
        RowCount = rowCount;
        VisualizationConfig = visualizationConfig;
        ExecutionTime = DateTime.UtcNow - ExecutionStartedAt.Value;
        ExecutionCompletedAt = DateTime.UtcNow;
        Status = QueryStatus.Executed;
        
        // Clear any previous error information
        ErrorMessage = null;
        ErrorStackTrace = null;
    }

    /// <summary>
    /// Marks the query session as failed
    /// </summary>
    public void MarkAsFailed(string errorMessage, string? stackTrace = null)
    {
        ErrorMessage = errorMessage;
        ErrorStackTrace = stackTrace;
        Status = QueryStatus.Failed;
        
        if (ExecutionStartedAt.HasValue && !ExecutionCompletedAt.HasValue)
        {
            ExecutionCompletedAt = DateTime.UtcNow;
            ExecutionTime = ExecutionCompletedAt.Value - ExecutionStartedAt.Value;
        }
    }

    /// <summary>
    /// Cancels the query session
    /// </summary>
    public void Cancel()
    {
        Status = QueryStatus.Cancelled;
        
        if (ExecutionStartedAt.HasValue && !ExecutionCompletedAt.HasValue)
        {
            ExecutionCompletedAt = DateTime.UtcNow;
            ExecutionTime = ExecutionCompletedAt.Value - ExecutionStartedAt.Value;
        }
    }

    /// <summary>
    /// Adds user feedback to the query session
    /// </summary>
    public void AddUserFeedback(int rating, string? comments = null)
    {
        if (rating < 1 || rating > 5)
            throw new ValidationException(nameof(rating), "Rating must be between 1 and 5");

        UserRating = rating;
        UserComments = comments?.Trim();
    }

    /// <summary>
    /// Marks this session as used for training
    /// </summary>
    public void MarkAsUsedForTraining()
    {
        if (Status != QueryStatus.Executed)
            throw new InvalidStateOperationException("MarkAsUsedForTraining", Status.ToString());

        if (UserRating.HasValue && UserRating.Value < 3)
            throw new BusinessRuleException("TrainingQuality", "Cannot use sessions with low ratings for training");

        UsedForTraining = true;
    }

    /// <summary>
    /// Gets the total processing time (generation + execution)
    /// </summary>
    public TimeSpan? GetTotalProcessingTime()
    {
        if (GenerationStartedAt == null) return null;
        
        var endTime = ExecutionCompletedAt ?? GenerationCompletedAt ?? DateTime.UtcNow;
        return endTime - GenerationStartedAt.Value;
    }

    /// <summary>
    /// Gets the generation time
    /// </summary>
    public TimeSpan? GetGenerationTime()
    {
        if (GenerationStartedAt == null || GenerationCompletedAt == null) return null;
        
        return GenerationCompletedAt.Value - GenerationStartedAt.Value;
    }

    /// <summary>
    /// Checks if the session is in a terminal state
    /// </summary>
    public bool IsCompleted()
    {
        return Status is QueryStatus.Executed or QueryStatus.Failed or QueryStatus.Cancelled;
    }

    /// <summary>
    /// Checks if the session was successful
    /// </summary>
    public bool IsSuccessful()
    {
        return Status == QueryStatus.Executed && string.IsNullOrEmpty(ErrorMessage);
    }

    /// <summary>
    /// Gets the follow-up questions as a list
    /// </summary>
    public List<string> GetFollowUpQuestions()
    {
        if (string.IsNullOrEmpty(FollowUpQuestions))
            return new List<string>();

        try
        {
            return JsonSerializer.Deserialize<List<string>>(FollowUpQuestions) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    /// <summary>
    /// Gets the results as a typed object
    /// </summary>
    public T? GetResults<T>() where T : class
    {
        if (string.IsNullOrEmpty(Results))
            return null;

        try
        {
            return JsonSerializer.Deserialize<T>(Results);
        }
        catch
        {
            return null;
        }
    }
}
