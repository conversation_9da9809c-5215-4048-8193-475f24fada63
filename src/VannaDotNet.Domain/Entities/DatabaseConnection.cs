using System;
using System.Collections.Generic;
using System.Linq;
using VannaDotNet.Domain.Common;
using VannaDotNet.Domain.Enums;
using VannaDotNet.Domain.Exceptions;

namespace VannaDotNet.Domain.Entities;

/// <summary>
/// Represents a database connection configuration
/// </summary>
public class DatabaseConnection : BaseEntity
{
    private readonly List<string> _allowedSchemas = new();

    /// <summary>
    /// Display name for the database connection
    /// </summary>
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// Type of database (PostgreSQL, SQL Server, etc.)
    /// </summary>
    public DatabaseType Type { get; private set; }

    /// <summary>
    /// Encrypted connection string
    /// </summary>
    public string ConnectionString { get; private set; } = string.Empty;

    /// <summary>
    /// Optional description of the database
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// Database host/server name
    /// </summary>
    public string Host { get; private set; } = string.Empty;

    /// <summary>
    /// Database port
    /// </summary>
    public int Port { get; private set; }

    /// <summary>
    /// Database name
    /// </summary>
    public string DatabaseName { get; private set; } = string.Empty;

    /// <summary>
    /// Username for database connection
    /// </summary>
    public string Username { get; private set; } = string.Empty;

    /// <summary>
    /// Indicates if the connection is read-only
    /// </summary>
    public bool IsReadOnly { get; private set; } = true;

    /// <summary>
    /// Indicates if the connection is currently active
    /// </summary>
    public bool IsActive { get; private set; } = true;

    /// <summary>
    /// Maximum number of concurrent connections allowed
    /// </summary>
    public int MaxConnections { get; private set; } = 10;

    /// <summary>
    /// Connection timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; private set; } = 30;

    /// <summary>
    /// Query timeout in seconds
    /// </summary>
    public int QueryTimeoutSeconds { get; private set; } = 60;

    /// <summary>
    /// Maximum number of rows that can be returned by a query
    /// </summary>
    public int MaxRowLimit { get; private set; } = 10000;

    /// <summary>
    /// Schemas that are allowed to be queried (empty means all schemas)
    /// </summary>
    public IReadOnlyList<string> AllowedSchemas => _allowedSchemas.AsReadOnly();

    /// <summary>
    /// Last time the connection was tested
    /// </summary>
    public DateTime? LastTestedAt { get; private set; }

    /// <summary>
    /// Result of the last connection test
    /// </summary>
    public bool? LastTestResult { get; private set; }

    /// <summary>
    /// Error message from the last connection test (if failed)
    /// </summary>
    public string? LastTestError { get; private set; }

    /// <summary>
    /// Number of successful queries executed
    /// </summary>
    public long SuccessfulQueries { get; private set; }

    /// <summary>
    /// Number of failed queries
    /// </summary>
    public long FailedQueries { get; private set; }

    /// <summary>
    /// Total execution time for all queries
    /// </summary>
    public TimeSpan TotalExecutionTime { get; private set; }

    /// <summary>
    /// Environment this connection belongs to (dev, staging, prod)
    /// </summary>
    public string Environment { get; private set; } = "development";

    // Private constructor for EF Core
    private DatabaseConnection() { }

    /// <summary>
    /// Creates a new database connection
    /// </summary>
    public static DatabaseConnection Create(
        string name,
        DatabaseType type,
        string connectionString,
        string host,
        int port,
        string databaseName,
        string username,
        string? description = null,
        bool isReadOnly = true,
        string environment = "development",
        string createdBy = "system")
    {
        ValidateConnectionParameters(name, connectionString, host, port, databaseName, username);

        var connection = new DatabaseConnection
        {
            Name = name.Trim(),
            Type = type,
            ConnectionString = connectionString.Trim(),
            Host = host.Trim(),
            Port = port,
            DatabaseName = databaseName.Trim(),
            Username = username.Trim(),
            Description = description?.Trim(),
            IsReadOnly = isReadOnly,
            Environment = environment.Trim().ToLowerInvariant()
        };

        connection.SetCreatedBy(createdBy);
        return connection;
    }

    /// <summary>
    /// Updates the connection configuration
    /// </summary>
    public void UpdateConfiguration(
        string name,
        string? description,
        bool isReadOnly,
        int maxConnections,
        int timeoutSeconds,
        int queryTimeoutSeconds,
        int maxRowLimit,
        string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ValidationException(nameof(name), "Name cannot be null or empty");

        if (maxConnections <= 0)
            throw new ValidationException(nameof(maxConnections), "Max connections must be greater than 0");

        if (timeoutSeconds <= 0)
            throw new ValidationException(nameof(timeoutSeconds), "Timeout seconds must be greater than 0");

        if (queryTimeoutSeconds <= 0)
            throw new ValidationException(nameof(queryTimeoutSeconds), "Query timeout seconds must be greater than 0");

        if (maxRowLimit <= 0)
            throw new ValidationException(nameof(maxRowLimit), "Max row limit must be greater than 0");

        Name = name.Trim();
        Description = description?.Trim();
        IsReadOnly = isReadOnly;
        MaxConnections = maxConnections;
        TimeoutSeconds = timeoutSeconds;
        QueryTimeoutSeconds = queryTimeoutSeconds;
        MaxRowLimit = maxRowLimit;

        SetUpdated(updatedBy);
    }

    /// <summary>
    /// Updates the connection string
    /// </summary>
    public void UpdateConnectionString(string connectionString, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(connectionString))
            throw new ValidationException(nameof(connectionString), "Connection string cannot be null or empty");

        ConnectionString = connectionString.Trim();
        SetUpdated(updatedBy);

        // Reset test results when connection string changes
        LastTestedAt = null;
        LastTestResult = null;
        LastTestError = null;
    }

    /// <summary>
    /// Adds an allowed schema
    /// </summary>
    public void AddAllowedSchema(string schemaName)
    {
        if (string.IsNullOrWhiteSpace(schemaName))
            throw new ValidationException(nameof(schemaName), "Schema name cannot be null or empty");

        var normalizedSchema = schemaName.Trim().ToLowerInvariant();
        if (!_allowedSchemas.Contains(normalizedSchema))
        {
            _allowedSchemas.Add(normalizedSchema);
        }
    }

    /// <summary>
    /// Removes an allowed schema
    /// </summary>
    public void RemoveAllowedSchema(string schemaName)
    {
        if (string.IsNullOrWhiteSpace(schemaName)) return;

        var normalizedSchema = schemaName.Trim().ToLowerInvariant();
        _allowedSchemas.Remove(normalizedSchema);
    }

    /// <summary>
    /// Clears all allowed schemas
    /// </summary>
    public void ClearAllowedSchemas()
    {
        _allowedSchemas.Clear();
    }

    /// <summary>
    /// Checks if a schema is allowed
    /// </summary>
    public bool IsSchemaAllowed(string schemaName)
    {
        if (string.IsNullOrWhiteSpace(schemaName)) return false;
        if (_allowedSchemas.Count == 0) return true; // No restrictions

        var normalizedSchema = schemaName.Trim().ToLowerInvariant();
        return _allowedSchemas.Contains(normalizedSchema);
    }

    /// <summary>
    /// Activates the database connection
    /// </summary>
    public void Activate(string updatedBy)
    {
        IsActive = true;
        SetUpdated(updatedBy);
    }

    /// <summary>
    /// Deactivates the database connection
    /// </summary>
    public void Deactivate(string updatedBy)
    {
        IsActive = false;
        SetUpdated(updatedBy);
    }

    /// <summary>
    /// Records the result of a connection test
    /// </summary>
    public void RecordTestResult(bool success, string? errorMessage = null)
    {
        LastTestedAt = DateTime.UtcNow;
        LastTestResult = success;
        LastTestError = success ? null : errorMessage;
    }

    /// <summary>
    /// Records a successful query execution
    /// </summary>
    public void RecordSuccessfulQuery(TimeSpan executionTime)
    {
        SuccessfulQueries++;
        TotalExecutionTime = TotalExecutionTime.Add(executionTime);
    }

    /// <summary>
    /// Records a failed query execution
    /// </summary>
    public void RecordFailedQuery()
    {
        FailedQueries++;
    }

    /// <summary>
    /// Gets the average query execution time
    /// </summary>
    public TimeSpan GetAverageExecutionTime()
    {
        if (SuccessfulQueries == 0) return TimeSpan.Zero;
        
        return TimeSpan.FromTicks(TotalExecutionTime.Ticks / SuccessfulQueries);
    }

    /// <summary>
    /// Gets the success rate of queries
    /// </summary>
    public double GetSuccessRate()
    {
        var totalQueries = SuccessfulQueries + FailedQueries;
        if (totalQueries == 0) return 0.0;
        
        return (double)SuccessfulQueries / totalQueries;
    }

    /// <summary>
    /// Checks if the connection needs testing
    /// </summary>
    public bool NeedsTesting(TimeSpan testInterval)
    {
        if (LastTestedAt == null) return true;
        
        return DateTime.UtcNow - LastTestedAt.Value > testInterval;
    }

    /// <summary>
    /// Gets the connection status
    /// </summary>
    public string GetStatus()
    {
        if (!IsActive) return "Inactive";
        if (LastTestResult == null) return "Untested";
        if (LastTestResult == true) return "Connected";
        return "Failed";
    }

    /// <summary>
    /// Validates connection parameters
    /// </summary>
    private static void ValidateConnectionParameters(
        string name, 
        string connectionString, 
        string host, 
        int port, 
        string databaseName, 
        string username)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ValidationException(nameof(name), "Name cannot be null or empty");

        if (string.IsNullOrWhiteSpace(connectionString))
            throw new ValidationException(nameof(connectionString), "Connection string cannot be null or empty");

        if (string.IsNullOrWhiteSpace(host))
            throw new ValidationException(nameof(host), "Host cannot be null or empty");

        if (port <= 0 || port > 65535)
            throw new ValidationException(nameof(port), "Port must be between 1 and 65535");

        if (string.IsNullOrWhiteSpace(databaseName))
            throw new ValidationException(nameof(databaseName), "Database name cannot be null or empty");

        if (string.IsNullOrWhiteSpace(username))
            throw new ValidationException(nameof(username), "Username cannot be null or empty");
    }
}
