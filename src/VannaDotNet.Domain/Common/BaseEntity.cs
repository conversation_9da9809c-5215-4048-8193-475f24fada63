using System;

namespace VannaDotNet.Domain.Common;

/// <summary>
/// Base entity class that provides common properties for all domain entities
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// Unique identifier for the entity
    /// </summary>
    public Guid Id { get; protected set; } = Guid.NewGuid();

    /// <summary>
    /// Timestamp when the entity was created
    /// </summary>
    public DateTime CreatedAt { get; protected set; } = DateTime.UtcNow;

    /// <summary>
    /// Timestamp when the entity was last updated
    /// </summary>
    public DateTime? UpdatedAt { get; protected set; }

    /// <summary>
    /// User who created the entity
    /// </summary>
    public string CreatedBy { get; protected set; } = string.Empty;

    /// <summary>
    /// User who last updated the entity
    /// </summary>
    public string? UpdatedBy { get; protected set; }

    /// <summary>
    /// Indicates if the entity has been soft deleted
    /// </summary>
    public bool IsDeleted { get; protected set; }

    /// <summary>
    /// Timestamp when the entity was deleted
    /// </summary>
    public DateTime? DeletedAt { get; protected set; }

    /// <summary>
    /// User who deleted the entity
    /// </summary>
    public string? DeletedBy { get; protected set; }

    /// <summary>
    /// Updates the entity's modification timestamp and user
    /// </summary>
    /// <param name="updatedBy">User performing the update</param>
    protected void SetUpdated(string updatedBy)
    {
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
    }

    /// <summary>
    /// Marks the entity as deleted (soft delete)
    /// </summary>
    /// <param name="deletedBy">User performing the deletion</param>
    public virtual void Delete(string deletedBy)
    {
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        DeletedBy = deletedBy;
    }

    /// <summary>
    /// Restores a soft-deleted entity
    /// </summary>
    /// <param name="restoredBy">User performing the restoration</param>
    public virtual void Restore(string restoredBy)
    {
        IsDeleted = false;
        DeletedAt = null;
        DeletedBy = null;
        SetUpdated(restoredBy);
    }

    /// <summary>
    /// Sets the created by user (used during entity creation)
    /// </summary>
    /// <param name="createdBy">User creating the entity</param>
    protected void SetCreatedBy(string createdBy)
    {
        CreatedBy = createdBy;
    }

    public override bool Equals(object? obj)
    {
        if (obj is not BaseEntity other)
            return false;

        if (ReferenceEquals(this, other))
            return true;

        if (GetType() != other.GetType())
            return false;

        return Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    public static bool operator ==(BaseEntity? left, BaseEntity? right)
    {
        return Equals(left, right);
    }

    public static bool operator !=(BaseEntity? left, BaseEntity? right)
    {
        return !Equals(left, right);
    }
}
