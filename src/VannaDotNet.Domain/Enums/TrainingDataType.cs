namespace VannaDotNet.Domain.Enums;

/// <summary>
/// Types of training data that can be stored in the system
/// </summary>
public enum TrainingDataType
{
    /// <summary>
    /// Data Definition Language statements (CREATE TABLE, ALTER TABLE, etc.)
    /// </summary>
    Ddl = 1,

    /// <summary>
    /// Business documentation and context information
    /// </summary>
    Documentation = 2,

    /// <summary>
    /// Question and SQL query pairs for training
    /// </summary>
    QuestionSql = 3,

    /// <summary>
    /// Database schema information
    /// </summary>
    Schema = 4,

    /// <summary>
    /// Sample data for context
    /// </summary>
    SampleData = 5
}

/// <summary>
/// Status of query execution
/// </summary>
public enum QueryStatus
{
    /// <summary>
    /// Query has been created but not yet processed
    /// </summary>
    Created = 1,

    /// <summary>
    /// Query is being processed by the LLM
    /// </summary>
    Processing = 2,

    /// <summary>
    /// SQL has been generated successfully
    /// </summary>
    Generated = 3,

    /// <summary>
    /// SQL has been validated
    /// </summary>
    Validated = 4,

    /// <summary>
    /// Query has been executed successfully
    /// </summary>
    Executed = 5,

    /// <summary>
    /// Query execution failed
    /// </summary>
    Failed = 6,

    /// <summary>
    /// Query was cancelled
    /// </summary>
    Cancelled = 7
}

/// <summary>
/// Types of SQL queries
/// </summary>
public enum SqlQueryType
{
    /// <summary>
    /// SELECT statement
    /// </summary>
    Select = 1,

    /// <summary>
    /// INSERT statement
    /// </summary>
    Insert = 2,

    /// <summary>
    /// UPDATE statement
    /// </summary>
    Update = 3,

    /// <summary>
    /// DELETE statement
    /// </summary>
    Delete = 4,

    /// <summary>
    /// CREATE statement
    /// </summary>
    Create = 5,

    /// <summary>
    /// ALTER statement
    /// </summary>
    Alter = 6,

    /// <summary>
    /// DROP statement
    /// </summary>
    Drop = 7,

    /// <summary>
    /// WITH clause (Common Table Expression)
    /// </summary>
    With = 8,

    /// <summary>
    /// Unknown or unsupported query type
    /// </summary>
    Unknown = 99
}

/// <summary>
/// Database connection types supported by the system
/// </summary>
public enum DatabaseType
{
    /// <summary>
    /// PostgreSQL database
    /// </summary>
    PostgreSql = 1,

    /// <summary>
    /// Microsoft SQL Server
    /// </summary>
    SqlServer = 2,

    /// <summary>
    /// MySQL database
    /// </summary>
    MySql = 3,

    /// <summary>
    /// SQLite database
    /// </summary>
    Sqlite = 4,

    /// <summary>
    /// Oracle database
    /// </summary>
    Oracle = 5,

    /// <summary>
    /// Google BigQuery
    /// </summary>
    BigQuery = 6,

    /// <summary>
    /// Snowflake data warehouse
    /// </summary>
    Snowflake = 7,

    /// <summary>
    /// DuckDB database
    /// </summary>
    DuckDb = 8,

    /// <summary>
    /// ClickHouse database
    /// </summary>
    ClickHouse = 9
}

/// <summary>
/// LLM provider types
/// </summary>
public enum LlmProvider
{
    /// <summary>
    /// OpenAI GPT models
    /// </summary>
    OpenAi = 1,

    /// <summary>
    /// Azure OpenAI Service
    /// </summary>
    AzureOpenAi = 2,

    /// <summary>
    /// Anthropic Claude models
    /// </summary>
    Anthropic = 3,

    /// <summary>
    /// Google Gemini models
    /// </summary>
    Gemini = 4,

    /// <summary>
    /// Local Ollama models
    /// </summary>
    Ollama = 5,

    /// <summary>
    /// Hugging Face models
    /// </summary>
    HuggingFace = 6
}

/// <summary>
/// Vector store provider types
/// </summary>
public enum VectorStoreProvider
{
    /// <summary>
    /// Qdrant vector database
    /// </summary>
    Qdrant = 1,

    /// <summary>
    /// ChromaDB vector database
    /// </summary>
    ChromaDb = 2,

    /// <summary>
    /// Pinecone vector database
    /// </summary>
    Pinecone = 3,

    /// <summary>
    /// Weaviate vector database
    /// </summary>
    Weaviate = 4,

    /// <summary>
    /// FAISS vector index
    /// </summary>
    Faiss = 5,

    /// <summary>
    /// Milvus vector database
    /// </summary>
    Milvus = 6,

    /// <summary>
    /// Azure Cognitive Search
    /// </summary>
    AzureSearch = 7,

    /// <summary>
    /// PostgreSQL with pgvector extension
    /// </summary>
    PgVector = 8
}
