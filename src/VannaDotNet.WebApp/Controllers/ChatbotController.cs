using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using VannaDotNet.WebApp.Data;
using VannaDotNet.WebApp.DTOs;
using VannaDotNet.WebApp.Models;
using VannaDotNet.WebApp.Services;

namespace VannaDotNet.WebApp.Controllers;

/// <summary>
/// Chatbot controller for AI-powered database queries
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
public class ChatbotController : ControllerBase
{
    private readonly IVannaDotNetApiService _vannaApiService;
    private readonly IRateLimitService _rateLimitService;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<ChatbotController> _logger;
    private readonly IConfiguration _configuration;

    public ChatbotController(
        IVannaDotNetApiService vannaApiService,
        IRateLimitService rateLimitService,
        ApplicationDbContext context,
        ILogger<ChatbotController> logger,
        IConfiguration configuration)
    {
        _vannaApiService = vannaApiService;
        _rateLimitService = rateLimitService;
        _context = context;
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Ask a natural language question about the database
    /// </summary>
    /// <param name="request">Question request</param>
    /// <returns>SQL query and results</returns>
    [HttpPost("ask")]
    [ProducesResponseType(typeof(ChatbotResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
    public async Task<ActionResult<ChatbotResponse>> AskQuestion([FromBody] ChatbotRequest request)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            // Check rate limiting
            var rateLimitConfig = _configuration.GetSection("RateLimit").Get<RateLimitConfiguration>() ?? new();
            var isAllowed = await _rateLimitService.IsAllowedAsync(
                userId, 
                "text-to-sql", 
                rateLimitConfig.TextToSqlMaxRequests,
                TimeSpan.FromHours(rateLimitConfig.TextToSqlWindowHours));

            if (!isAllowed)
            {
                var status = await _rateLimitService.GetStatusAsync(
                    userId, 
                    "text-to-sql", 
                    rateLimitConfig.TextToSqlMaxRequests,
                    TimeSpan.FromHours(rateLimitConfig.TextToSqlWindowHours));

                return StatusCode(StatusCodes.Status429TooManyRequests, new ChatbotResponse
                {
                    Success = false,
                    Message = "Rate limit exceeded. Please try again later.",
                    RateLimitInfo = new RateLimitInfo
                    {
                        RequestCount = status.RequestCount,
                        MaxRequests = status.MaxRequests,
                        RemainingRequests = status.RemainingRequests,
                        ResetTime = status.ResetTime
                    }
                });
            }

            // Get user's database metadata
            var databaseMetadata = await _context.UserDatabaseMetadata
                .FirstOrDefaultAsync(d => d.Id == request.DatabaseMetadataId && d.UserId == userId);

            if (databaseMetadata == null)
            {
                return BadRequest(new ChatbotResponse
                {
                    Success = false,
                    Message = "Database metadata not found or access denied"
                });
            }

            // Create VannaDotNet API request
            var apiRequest = new AskQuestionRequest
            {
                Question = request.Question,
                DatabaseConnectionId = request.DatabaseMetadataId, // Using metadata ID as connection ID
                Options = request.Options,
                CreatedBy = userId
            };

            // Call VannaDotNet API
            var apiResult = await _vannaApiService.AskQuestionAsync(apiRequest);

            // Record the action for rate limiting
            await _rateLimitService.RecordActionAsync(
                userId, 
                "text-to-sql", 
                TimeSpan.FromHours(rateLimitConfig.TextToSqlWindowHours));

            // Save query history
            var queryHistory = new QueryHistory
            {
                UserId = userId,
                DatabaseMetadataId = request.DatabaseMetadataId,
                Question = request.Question,
                GeneratedSql = apiResult.GeneratedSql,
                Results = apiResult.Results != null ? System.Text.Json.JsonSerializer.Serialize(apiResult.Results) : null,
                IsSuccessful = apiResult.Success,
                ErrorMessage = apiResult.Error,
                Confidence = apiResult.Confidence,
                ExecutionTime = apiResult.ExecutionTime,
                RowCount = apiResult.RowCount,
                CreatedAt = DateTime.UtcNow
            };

            _context.QueryHistory.Add(queryHistory);
            await _context.SaveChangesAsync();

            // Get rate limit status for response
            var rateLimitStatus = await _rateLimitService.GetStatusAsync(
                userId, 
                "text-to-sql", 
                rateLimitConfig.TextToSqlMaxRequests,
                TimeSpan.FromHours(rateLimitConfig.TextToSqlWindowHours));

            var response = new ChatbotResponse
            {
                Success = apiResult.Success,
                Message = apiResult.Message,
                Question = apiResult.Question,
                GeneratedSql = apiResult.GeneratedSql,
                Results = apiResult.Results,
                Confidence = apiResult.Confidence,
                Explanation = apiResult.Explanation,
                FollowUpQuestions = apiResult.FollowUpQuestions,
                RowCount = apiResult.RowCount,
                ExecutionTime = apiResult.ExecutionTime,
                Error = apiResult.Error,
                SessionId = queryHistory.Id,
                Columns = apiResult.Columns,
                RateLimitInfo = new RateLimitInfo
                {
                    RequestCount = rateLimitStatus.RequestCount,
                    MaxRequests = rateLimitStatus.MaxRequests,
                    RemainingRequests = rateLimitStatus.RemainingRequests,
                    ResetTime = rateLimitStatus.ResetTime
                }
            };

            _logger.LogInformation("User {UserId} asked question: {Question}, Success: {Success}", 
                userId, request.Question, apiResult.Success);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing chatbot question for user {UserId}", userId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ChatbotResponse
            {
                Success = false,
                Message = "An error occurred while processing your question",
                Error = "Internal server error"
            });
        }
    }

    /// <summary>
    /// Generate SQL from natural language without executing
    /// </summary>
    /// <param name="request">SQL generation request</param>
    /// <returns>Generated SQL query</returns>
    [HttpPost("generate-sql")]
    [ProducesResponseType(typeof(GenerateSqlResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
    public async Task<ActionResult<GenerateSqlResponse>> GenerateSql([FromBody] GenerateSqlRequestDto request)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            // Check rate limiting
            var rateLimitConfig = _configuration.GetSection("RateLimit").Get<RateLimitConfiguration>() ?? new();
            var isAllowed = await _rateLimitService.IsAllowedAsync(
                userId, 
                "text-to-sql", 
                rateLimitConfig.TextToSqlMaxRequests,
                TimeSpan.FromHours(rateLimitConfig.TextToSqlWindowHours));

            if (!isAllowed)
            {
                return StatusCode(StatusCodes.Status429TooManyRequests, new GenerateSqlResponse
                {
                    Success = false,
                    Message = "Rate limit exceeded. Please try again later."
                });
            }

            // Get user's database metadata
            var databaseMetadata = await _context.UserDatabaseMetadata
                .FirstOrDefaultAsync(d => d.Id == request.DatabaseMetadataId && d.UserId == userId);

            if (databaseMetadata == null)
            {
                return BadRequest(new GenerateSqlResponse
                {
                    Success = false,
                    Message = "Database metadata not found or access denied"
                });
            }

            // Create VannaDotNet API request
            var apiRequest = new GenerateSqlRequest
            {
                Question = request.Question,
                DatabaseConnectionId = request.DatabaseMetadataId,
                Options = request.Options,
                CreatedBy = userId
            };

            // Call VannaDotNet API
            var apiResult = await _vannaApiService.GenerateSqlAsync(apiRequest);

            // Record the action for rate limiting
            await _rateLimitService.RecordActionAsync(
                userId, 
                "text-to-sql", 
                TimeSpan.FromHours(rateLimitConfig.TextToSqlWindowHours));

            var response = new GenerateSqlResponse
            {
                Success = apiResult.Success,
                Message = apiResult.Message,
                GeneratedSql = apiResult.GeneratedSql,
                Confidence = apiResult.Confidence,
                Explanation = apiResult.Explanation,
                FollowUpQuestions = apiResult.FollowUpQuestions,
                Error = apiResult.Error
            };

            _logger.LogInformation("User {UserId} generated SQL for question: {Question}, Success: {Success}", 
                userId, request.Question, apiResult.Success);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating SQL for user {UserId}", userId);
            return StatusCode(StatusCodes.Status500InternalServerError, new GenerateSqlResponse
            {
                Success = false,
                Message = "An error occurred while generating SQL",
                Error = "Internal server error"
            });
        }
    }

    /// <summary>
    /// Get query history for the current user
    /// </summary>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="databaseMetadataId">Optional database filter</param>
    /// <returns>Paginated query history</returns>
    [HttpGet("history")]
    [ProducesResponseType(typeof(PagedResult<QueryHistoryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<PagedResult<QueryHistoryDto>>> GetQueryHistory(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] Guid? databaseMetadataId = null)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        try
        {
            IQueryable<QueryHistory> query = _context.QueryHistory
                .Where(q => q.UserId == userId)
                .Include(q => q.DatabaseMetadata);

            if (databaseMetadataId.HasValue)
            {
                query = query.Where(q => q.DatabaseMetadataId == databaseMetadataId.Value);
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderByDescending(q => q.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(q => new QueryHistoryDto
                {
                    Id = q.Id,
                    Question = q.Question,
                    GeneratedSql = q.GeneratedSql,
                    IsSuccessful = q.IsSuccessful,
                    ErrorMessage = q.ErrorMessage,
                    Confidence = q.Confidence,
                    ExecutionTime = q.ExecutionTime,
                    RowCount = q.RowCount,
                    CreatedAt = q.CreatedAt,
                    DatabaseName = q.DatabaseMetadata.Name
                })
                .ToListAsync();

            var result = new PagedResult<QueryHistoryDto>
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting query history for user {UserId}", userId);
            return StatusCode(StatusCodes.Status500InternalServerError, new { message = "Error retrieving query history" });
        }
    }

    /// <summary>
    /// Get rate limit status for the current user
    /// </summary>
    /// <returns>Rate limit information</returns>
    [HttpGet("rate-limit-status")]
    [ProducesResponseType(typeof(RateLimitInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<RateLimitInfo>> GetRateLimitStatus()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        try
        {
            var rateLimitConfig = _configuration.GetSection("RateLimit").Get<RateLimitConfiguration>() ?? new();
            var status = await _rateLimitService.GetStatusAsync(
                userId, 
                "text-to-sql", 
                rateLimitConfig.TextToSqlMaxRequests,
                TimeSpan.FromHours(rateLimitConfig.TextToSqlWindowHours));

            var rateLimitInfo = new RateLimitInfo
            {
                RequestCount = status.RequestCount,
                MaxRequests = status.MaxRequests,
                RemainingRequests = status.RemainingRequests,
                ResetTime = status.ResetTime
            };

            return Ok(rateLimitInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rate limit status for user {UserId}", userId);
            return StatusCode(StatusCodes.Status500InternalServerError, new { message = "Error retrieving rate limit status" });
        }
    }
}
