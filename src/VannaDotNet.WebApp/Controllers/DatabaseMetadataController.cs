using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using VannaDotNet.WebApp.Data;
using VannaDotNet.WebApp.DTOs;
using VannaDotNet.WebApp.Models;
using VannaDotNet.WebApp.Services;

namespace VannaDotNet.WebApp.Controllers;

/// <summary>
/// Controller for managing user database metadata
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
public class DatabaseMetadataController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly IRateLimitService _rateLimitService;
    private readonly IVannaDotNetApiService _vannaApiService;
    private readonly ILogger<DatabaseMetadataController> _logger;
    private readonly IConfiguration _configuration;

    public DatabaseMetadataController(
        ApplicationDbContext context,
        IRateLimitService rateLimitService,
        IVannaDotNetApiService vannaApiService,
        ILogger<DatabaseMetadataController> logger,
        IConfiguration configuration)
    {
        _context = context;
        _rateLimitService = rateLimitService;
        _vannaApiService = vannaApiService;
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Get all database metadata for the current user
    /// </summary>
    /// <returns>List of user's database metadata</returns>
    [HttpGet]
    [ProducesResponseType(typeof(List<DatabaseMetadataDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<List<DatabaseMetadataDto>>> GetDatabaseMetadata()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        try
        {
            var metadata = await _context.UserDatabaseMetadata
                .Where(d => d.UserId == userId && d.IsActive)
                .Include(d => d.Tables)
                    .ThenInclude(t => t.Columns)
                        .ThenInclude(c => c.CodeMappings)
                .Include(d => d.Tables)
                    .ThenInclude(t => t.ParentRelationships)
                .Include(d => d.Tables)
                    .ThenInclude(t => t.ChildRelationships)
                .OrderBy(d => d.Name)
                .ToListAsync();

            var result = metadata.Select(d => new DatabaseMetadataDto
            {
                Id = d.Id,
                Name = d.Name,
                Description = d.Description,
                DatabaseType = d.DatabaseType,
                IsActive = d.IsActive,
                CreatedAt = d.CreatedAt,
                UpdatedAt = d.UpdatedAt,
                Tables = d.Tables.Select(t => new TableMetadataDto
                {
                    Id = t.Id,
                    TableName = t.TableName,
                    SchemaName = t.SchemaName,
                    Description = t.Description,
                    DisplayName = t.DisplayName,
                    CreatedAt = t.CreatedAt,
                    UpdatedAt = t.UpdatedAt,
                    Columns = t.Columns.Select(c => new ColumnMetadataDto
                    {
                        Id = c.Id,
                        ColumnName = c.ColumnName,
                        DataType = c.DataType,
                        DisplayName = c.DisplayName,
                        Description = c.Description,
                        IsNullable = c.IsNullable,
                        IsPrimaryKey = c.IsPrimaryKey,
                        IsForeignKey = c.IsForeignKey,
                        MaxLength = c.MaxLength,
                        DefaultValue = c.DefaultValue,
                        CodeMappings = c.CodeMappings.Select(cm => new CodeMappingDto
                        {
                            Id = cm.Id,
                            Code = cm.Code,
                            Description = cm.Description,
                            SortOrder = cm.SortOrder,
                            IsActive = cm.IsActive
                        }).ToList()
                    }).ToList(),
                    Relationships = t.ParentRelationships.Concat(t.ChildRelationships)
                        .Select(r => new TableRelationshipDto
                        {
                            Id = r.Id,
                            ParentTable = r.ParentTable.TableName,
                            ChildTable = r.ChildTable.TableName,
                            ParentColumn = r.ParentColumn,
                            ChildColumn = r.ChildColumn,
                            RelationshipType = r.RelationshipType,
                            Description = r.Description
                        }).ToList()
                }).ToList()
            }).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting database metadata for user {UserId}", userId);
            return StatusCode(StatusCodes.Status500InternalServerError, new { message = "Error retrieving database metadata" });
        }
    }

    /// <summary>
    /// Get specific database metadata by ID
    /// </summary>
    /// <param name="id">Database metadata ID</param>
    /// <returns>Database metadata details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(DatabaseMetadataDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<DatabaseMetadataDto>> GetDatabaseMetadata(Guid id)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        try
        {
            var metadata = await _context.UserDatabaseMetadata
                .Where(d => d.Id == id && d.UserId == userId)
                .Include(d => d.Tables)
                    .ThenInclude(t => t.Columns)
                        .ThenInclude(c => c.CodeMappings)
                .Include(d => d.Tables)
                    .ThenInclude(t => t.ParentRelationships)
                .Include(d => d.Tables)
                    .ThenInclude(t => t.ChildRelationships)
                .FirstOrDefaultAsync();

            if (metadata == null)
            {
                return NotFound(new { message = "Database metadata not found" });
            }

            var result = new DatabaseMetadataDto
            {
                Id = metadata.Id,
                Name = metadata.Name,
                Description = metadata.Description,
                DatabaseType = metadata.DatabaseType,
                IsActive = metadata.IsActive,
                CreatedAt = metadata.CreatedAt,
                UpdatedAt = metadata.UpdatedAt,
                Tables = metadata.Tables.Select(t => new TableMetadataDto
                {
                    Id = t.Id,
                    TableName = t.TableName,
                    SchemaName = t.SchemaName,
                    Description = t.Description,
                    DisplayName = t.DisplayName,
                    CreatedAt = t.CreatedAt,
                    UpdatedAt = t.UpdatedAt,
                    Columns = t.Columns.Select(c => new ColumnMetadataDto
                    {
                        Id = c.Id,
                        ColumnName = c.ColumnName,
                        DataType = c.DataType,
                        DisplayName = c.DisplayName,
                        Description = c.Description,
                        IsNullable = c.IsNullable,
                        IsPrimaryKey = c.IsPrimaryKey,
                        IsForeignKey = c.IsForeignKey,
                        MaxLength = c.MaxLength,
                        DefaultValue = c.DefaultValue,
                        CodeMappings = c.CodeMappings.Select(cm => new CodeMappingDto
                        {
                            Id = cm.Id,
                            Code = cm.Code,
                            Description = cm.Description,
                            SortOrder = cm.SortOrder,
                            IsActive = cm.IsActive
                        }).ToList()
                    }).ToList(),
                    Relationships = t.ParentRelationships.Concat(t.ChildRelationships)
                        .Select(r => new TableRelationshipDto
                        {
                            Id = r.Id,
                            ParentTable = r.ParentTable.TableName,
                            ChildTable = r.ChildTable.TableName,
                            ParentColumn = r.ParentColumn,
                            ChildColumn = r.ChildColumn,
                            RelationshipType = r.RelationshipType,
                            Description = r.Description
                        }).ToList()
                }).ToList()
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting database metadata {Id} for user {UserId}", id, userId);
            return StatusCode(StatusCodes.Status500InternalServerError, new { message = "Error retrieving database metadata" });
        }
    }

    /// <summary>
    /// Create new database metadata
    /// </summary>
    /// <param name="request">Database metadata creation request</param>
    /// <returns>Created database metadata</returns>
    [HttpPost]
    [ProducesResponseType(typeof(DatabaseMetadataDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
    public async Task<ActionResult<DatabaseMetadataDto>> CreateDatabaseMetadata([FromBody] CreateDatabaseMetadataRequest request)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            // Check rate limiting
            var rateLimitConfig = _configuration.GetSection("RateLimit").Get<RateLimitConfiguration>() ?? new();
            var isAllowed = await _rateLimitService.IsAllowedAsync(
                userId, 
                "database-metadata", 
                rateLimitConfig.DatabaseMetadataMaxRequests,
                TimeSpan.FromHours(rateLimitConfig.DatabaseMetadataWindowHours));

            if (!isAllowed)
            {
                return StatusCode(StatusCodes.Status429TooManyRequests, new { message = "Rate limit exceeded for database metadata operations" });
            }

            // Check if database with same name already exists for user
            var existingDatabase = await _context.UserDatabaseMetadata
                .FirstOrDefaultAsync(d => d.UserId == userId && d.Name == request.Name);

            if (existingDatabase != null)
            {
                return BadRequest(new { message = "Database with this name already exists" });
            }

            var metadata = new UserDatabaseMetadata
            {
                UserId = userId,
                Name = request.Name,
                Description = request.Description,
                ConnectionString = request.ConnectionString,
                DatabaseType = request.DatabaseType,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.UserDatabaseMetadata.Add(metadata);
            await _context.SaveChangesAsync();

            // Record the action for rate limiting
            await _rateLimitService.RecordActionAsync(
                userId, 
                "database-metadata", 
                TimeSpan.FromHours(rateLimitConfig.DatabaseMetadataWindowHours));

            var result = new DatabaseMetadataDto
            {
                Id = metadata.Id,
                Name = metadata.Name,
                Description = metadata.Description,
                DatabaseType = metadata.DatabaseType,
                IsActive = metadata.IsActive,
                CreatedAt = metadata.CreatedAt,
                UpdatedAt = metadata.UpdatedAt,
                Tables = new List<TableMetadataDto>()
            };

            _logger.LogInformation("User {UserId} created database metadata {DatabaseId}", userId, metadata.Id);

            return CreatedAtAction(nameof(GetDatabaseMetadata), new { id = metadata.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating database metadata for user {UserId}", userId);
            return StatusCode(StatusCodes.Status500InternalServerError, new { message = "Error creating database metadata" });
        }
    }

    /// <summary>
    /// Test database connection
    /// </summary>
    /// <param name="request">Connection test request</param>
    /// <returns>Connection test result</returns>
    [HttpPost("test-connection")]
    [ProducesResponseType(typeof(TestConnectionResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TestConnectionResult>> TestConnection([FromBody] TestConnectionRequest request)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var result = await _vannaApiService.TestConnectionAsync(request);
            
            _logger.LogInformation("User {UserId} tested database connection, Success: {Success}", userId, result);

            return Ok(new TestConnectionResult
            {
                Success = result,
                Message = result ? "Connection successful" : "Connection failed"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing database connection for user {UserId}", userId);
            return Ok(new TestConnectionResult
            {
                Success = false,
                Message = "Connection test failed",
                Error = ex.Message
            });
        }
    }
}
