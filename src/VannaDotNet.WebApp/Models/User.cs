using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace VannaDotNet.WebApp.Models;

/// <summary>
/// Application user entity extending IdentityUser
/// </summary>
public class ApplicationUser : IdentityUser
{
    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;

    [MaxLength(500)]
    public string? Bio { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? LastLoginAt { get; set; }

    public bool IsActive { get; set; } = true;

    // Navigation properties
    public virtual ICollection<UserDatabaseMetadata> DatabaseMetadata { get; set; } = new List<UserDatabaseMetadata>();
    public virtual ICollection<QueryHistory> QueryHistory { get; set; } = new List<QueryHistory>();
    public virtual ICollection<RateLimitEntry> RateLimitEntries { get; set; } = new List<RateLimitEntry>();
}

/// <summary>
/// User's database metadata configuration
/// </summary>
public class UserDatabaseMetadata
{
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    [MaxLength(1000)]
    public string? Description { get; set; }

    [Required]
    public string ConnectionString { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string DatabaseType { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ApplicationUser User { get; set; } = null!;
    public virtual ICollection<TableMetadata> Tables { get; set; } = new List<TableMetadata>();
}

/// <summary>
/// Table metadata for user's database
/// </summary>
public class TableMetadata
{
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    public Guid DatabaseMetadataId { get; set; }

    [Required]
    [MaxLength(200)]
    public string TableName { get; set; } = string.Empty;

    [MaxLength(100)]
    public string? SchemaName { get; set; }

    [MaxLength(1000)]
    public string? Description { get; set; }

    [MaxLength(200)]
    public string? DisplayName { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual UserDatabaseMetadata DatabaseMetadata { get; set; } = null!;
    public virtual ICollection<ColumnMetadata> Columns { get; set; } = new List<ColumnMetadata>();
    public virtual ICollection<TableRelationship> ParentRelationships { get; set; } = new List<TableRelationship>();
    public virtual ICollection<TableRelationship> ChildRelationships { get; set; } = new List<TableRelationship>();
}

/// <summary>
/// Column metadata for tables
/// </summary>
public class ColumnMetadata
{
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    public Guid TableMetadataId { get; set; }

    [Required]
    [MaxLength(200)]
    public string ColumnName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string DataType { get; set; } = string.Empty;

    [MaxLength(200)]
    public string? DisplayName { get; set; }

    [MaxLength(1000)]
    public string? Description { get; set; }

    public bool IsNullable { get; set; } = true;

    public bool IsPrimaryKey { get; set; } = false;

    public bool IsForeignKey { get; set; } = false;

    public int? MaxLength { get; set; }

    public string? DefaultValue { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual TableMetadata Table { get; set; } = null!;
    public virtual ICollection<CodeMapping> CodeMappings { get; set; } = new List<CodeMapping>();
}

/// <summary>
/// Code/decode mappings for columns
/// </summary>
public class CodeMapping
{
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    public Guid ColumnMetadataId { get; set; }

    [Required]
    [MaxLength(100)]
    public string Code { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string Description { get; set; } = string.Empty;

    public int SortOrder { get; set; } = 0;

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ColumnMetadata Column { get; set; } = null!;
}

/// <summary>
/// Relationships between tables
/// </summary>
public class TableRelationship
{
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    public Guid ParentTableId { get; set; }

    [Required]
    public Guid ChildTableId { get; set; }

    [Required]
    [MaxLength(200)]
    public string ParentColumn { get; set; } = string.Empty;

    [Required]
    [MaxLength(200)]
    public string ChildColumn { get; set; } = string.Empty;

    [MaxLength(100)]
    public string RelationshipType { get; set; } = "OneToMany";

    [MaxLength(1000)]
    public string? Description { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual TableMetadata ParentTable { get; set; } = null!;
    public virtual TableMetadata ChildTable { get; set; } = null!;
}

/// <summary>
/// Query history for users
/// </summary>
public class QueryHistory
{
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public Guid DatabaseMetadataId { get; set; }

    [Required]
    [MaxLength(2000)]
    public string Question { get; set; } = string.Empty;

    [MaxLength(10000)]
    public string? GeneratedSql { get; set; }

    public string? Results { get; set; }

    public bool IsSuccessful { get; set; } = false;

    [MaxLength(2000)]
    public string? ErrorMessage { get; set; }

    public double? Confidence { get; set; }

    public TimeSpan? ExecutionTime { get; set; }

    public int? RowCount { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ApplicationUser User { get; set; } = null!;
    public virtual UserDatabaseMetadata DatabaseMetadata { get; set; } = null!;
}

/// <summary>
/// Rate limiting entries for users
/// </summary>
public class RateLimitEntry
{
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string Action { get; set; } = string.Empty;

    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public DateTime ExpiresAt { get; set; }

    // Navigation properties
    public virtual ApplicationUser User { get; set; } = null!;
}
