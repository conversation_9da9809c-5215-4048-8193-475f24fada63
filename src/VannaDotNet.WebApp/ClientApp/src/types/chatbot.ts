// Chatbot request and response types
export interface ChatbotRequest {
  question: string;
  databaseMetadataId: string;
  options?: GenerationOptions;
}

export interface ChatbotResponse {
  success: boolean;
  message: string;
  question?: string;
  generatedSql?: string;
  results?: Record<string, any>[];
  confidence?: number;
  explanation?: string;
  followUpQuestions?: string[];
  rowCount?: number;
  executionTime?: string;
  error?: string;
  sessionId?: string;
  columns?: ColumnInfo[];
  rateLimitInfo?: RateLimitInfo;
}

export interface GenerateSqlRequest {
  question: string;
  databaseMetadataId: string;
  options?: GenerationOptions;
}

export interface GenerateSqlResponse {
  success: boolean;
  message: string;
  generatedSql?: string;
  confidence?: number;
  explanation?: string;
  followUpQuestions?: string[];
  error?: string;
}

export interface GenerationOptions {
  temperature?: number;
  maxTokens?: number;
  includeExplanation?: boolean;
  includeFollowUpQuestions?: boolean;
  maxFollowUpQuestions?: number;
}

export interface ColumnInfo {
  name: string;
  dataType: string;
  displayName?: string;
  description?: string;
  isNullable: boolean;
}

export interface RateLimitInfo {
  requestCount: number;
  maxRequests: number;
  remainingRequests: number;
  resetTime: string;
}

export interface RateLimitStatus extends RateLimitInfo {
  isAllowed: boolean;
}

// Query history types
export interface QueryHistoryItem {
  id: string;
  question: string;
  generatedSql?: string;
  isSuccessful: boolean;
  errorMessage?: string;
  confidence?: number;
  executionTime?: string;
  rowCount?: number;
  createdAt: string;
  databaseName: string;
}

// Chat message types for UI
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'error' | 'system';
  content: string;
  timestamp: Date;
  sql?: string;
  results?: Record<string, any>[];
  columns?: ColumnInfo[];
  confidence?: number;
  explanation?: string;
  followUpQuestions?: string[];
  executionTime?: string;
  rowCount?: number;
  error?: string;
}

// Form data types
export interface ChatFormData {
  question: string;
  databaseMetadataId: string;
  includeExplanation: boolean;
  includeFollowUpQuestions: boolean;
  temperature: number;
}

// Chat session types
export interface ChatSession {
  id: string;
  databaseMetadataId: string;
  databaseName: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

// Statistics types
export interface QueryStatistics {
  totalQueries: number;
  successfulQueries: number;
  failedQueries: number;
  averageConfidence: number;
  averageExecutionTime: number;
  mostUsedDatabase: string;
  queriesThisWeek: number;
  queriesThisMonth: number;
}
