// Database metadata types
export interface DatabaseMetadata {
  id: string;
  name: string;
  description?: string;
  databaseType: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  tables: TableMetadata[];
}

export interface TableMetadata {
  id: string;
  tableName: string;
  schemaName?: string;
  description?: string;
  displayName?: string;
  createdAt: string;
  updatedAt: string;
  columns: ColumnMetadata[];
  relationships: TableRelationship[];
}

export interface ColumnMetadata {
  id: string;
  columnName: string;
  dataType: string;
  displayName?: string;
  description?: string;
  isNullable: boolean;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
  maxLength?: number;
  defaultValue?: string;
  codeMappings: CodeMapping[];
}

export interface CodeMapping {
  id: string;
  code: string;
  description: string;
  sortOrder: number;
  isActive: boolean;
}

export interface TableRelationship {
  id: string;
  parentTable: string;
  childTable: string;
  parentColumn: string;
  childColumn: string;
  relationshipType: string;
  description?: string;
}

// Request types for creating/updating metadata
export interface CreateDatabaseMetadataRequest {
  name: string;
  description?: string;
  connectionString: string;
  databaseType: string;
}

export interface CreateTableMetadataRequest {
  tableName: string;
  schemaName?: string;
  description?: string;
  displayName?: string;
  columns: CreateColumnMetadataRequest[];
}

export interface CreateColumnMetadataRequest {
  columnName: string;
  dataType: string;
  displayName?: string;
  description?: string;
  isNullable: boolean;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
  maxLength?: number;
  defaultValue?: string;
  codeMappings: CreateCodeMappingRequest[];
}

export interface CreateCodeMappingRequest {
  code: string;
  description: string;
  sortOrder: number;
  isActive: boolean;
}

export interface CreateTableRelationshipRequest {
  parentTableId: string;
  childTableId: string;
  parentColumn: string;
  childColumn: string;
  relationshipType: string;
  description?: string;
}

// Connection testing types
export interface TestConnectionRequest {
  host: string;
  port: number;
  databaseName: string;
  username: string;
  password: string;
  type: string;
}

export interface TestConnectionResult {
  success: boolean;
  message: string;
  error?: string;
  responseTime?: string;
}

// Form data types
export interface DatabaseFormData {
  name: string;
  description: string;
  databaseType: string;
  host: string;
  port: number;
  databaseName: string;
  username: string;
  password: string;
}

export interface TableFormData {
  tableName: string;
  schemaName: string;
  description: string;
  displayName: string;
}

export interface ColumnFormData {
  columnName: string;
  dataType: string;
  displayName: string;
  description: string;
  isNullable: boolean;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
  maxLength: number | null;
  defaultValue: string;
}

export interface CodeMappingFormData {
  code: string;
  description: string;
  sortOrder: number;
  isActive: boolean;
}

export interface RelationshipFormData {
  parentTableId: string;
  childTableId: string;
  parentColumn: string;
  childColumn: string;
  relationshipType: string;
  description: string;
}

// Database type options
export const DATABASE_TYPES = [
  { value: 'postgresql', label: 'PostgreSQL' },
  { value: 'mysql', label: 'MySQL' },
  { value: 'sqlserver', label: 'SQL Server' },
  { value: 'oracle', label: 'Oracle' },
  { value: 'sqlite', label: 'SQLite' },
] as const;

export type DatabaseType = typeof DATABASE_TYPES[number]['value'];

// Data type options for columns
export const DATA_TYPES = [
  'VARCHAR',
  'CHAR',
  'TEXT',
  'INT',
  'INTEGER',
  'BIGINT',
  'SMALLINT',
  'DECIMAL',
  'NUMERIC',
  'FLOAT',
  'DOUBLE',
  'REAL',
  'BOOLEAN',
  'BIT',
  'DATE',
  'TIME',
  'DATETIME',
  'TIMESTAMP',
  'UUID',
  'JSON',
  'JSONB',
  'BLOB',
  'CLOB',
] as const;

export type DataType = typeof DATA_TYPES[number];

// Relationship type options
export const RELATIONSHIP_TYPES = [
  { value: 'OneToOne', label: 'One to One' },
  { value: 'OneToMany', label: 'One to Many' },
  { value: 'ManyToOne', label: 'Many to One' },
  { value: 'ManyToMany', label: 'Many to Many' },
] as const;

export type RelationshipType = typeof RELATIONSHIP_TYPES[number]['value'];
