import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  User,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  UpdateProfileRequest,
  UserProfile,
} from '../types/auth';
import {
  ChatbotRequest,
  ChatbotResponse,
  GenerateSqlRequest,
  GenerateSqlResponse,
  QueryHistoryItem,
  RateLimitStatus,
} from '../types/chatbot';
import {
  DatabaseMetadata,
  CreateDatabaseMetadataRequest,
  TestConnectionRequest,
  TestConnectionResult,
} from '../types/database';

// API Response wrapper
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
}

// Create axios instance
const createApiClient = (baseURL: string = '/api'): AxiosInstance => {
  const client = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  client.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for error handling
  client.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        // Token expired or invalid
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );

  return client;
};

const apiClient = createApiClient();

// Auth API
export const authApi = {
  setAuthToken: (token: string | null) => {
    if (token) {
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete apiClient.defaults.headers.common['Authorization'];
    }
  },

  login: async (credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> => {
    const response: AxiosResponse<AuthResponse> = await apiClient.post('/auth/login', credentials);
    return {
      success: true,
      message: 'Login successful',
      data: response.data,
    };
  },

  register: async (userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> => {
    const response: AxiosResponse<AuthResponse> = await apiClient.post('/auth/register', userData);
    return {
      success: true,
      message: 'Registration successful',
      data: response.data,
    };
  },

  logout: async (): Promise<ApiResponse> => {
    await apiClient.post('/auth/logout');
    return {
      success: true,
      message: 'Logout successful',
    };
  },

  refreshToken: async (token: string): Promise<ApiResponse<AuthResponse>> => {
    const response: AxiosResponse<AuthResponse> = await apiClient.post('/auth/refresh', { token });
    return {
      success: true,
      message: 'Token refreshed',
      data: response.data,
    };
  },

  getCurrentUser: async (): Promise<ApiResponse<User>> => {
    const response: AxiosResponse<User> = await apiClient.get('/auth/me');
    return {
      success: true,
      message: 'User retrieved',
      data: response.data,
    };
  },

  getUserProfile: async (): Promise<ApiResponse<UserProfile>> => {
    const response: AxiosResponse<UserProfile> = await apiClient.get('/auth/profile');
    return {
      success: true,
      message: 'Profile retrieved',
      data: response.data,
    };
  },

  updateProfile: async (profileData: UpdateProfileRequest): Promise<ApiResponse> => {
    await apiClient.put('/auth/profile', profileData);
    return {
      success: true,
      message: 'Profile updated successfully',
    };
  },

  validateToken: async (): Promise<ApiResponse> => {
    await apiClient.get('/auth/validate');
    return {
      success: true,
      message: 'Token is valid',
    };
  },
};

// Chatbot API
export const chatbotApi = {
  askQuestion: async (request: ChatbotRequest): Promise<ApiResponse<ChatbotResponse>> => {
    const response: AxiosResponse<ChatbotResponse> = await apiClient.post('/chatbot/ask', request);
    return {
      success: true,
      message: 'Question processed',
      data: response.data,
    };
  },

  generateSql: async (request: GenerateSqlRequest): Promise<ApiResponse<GenerateSqlResponse>> => {
    const response: AxiosResponse<GenerateSqlResponse> = await apiClient.post('/chatbot/generate-sql', request);
    return {
      success: true,
      message: 'SQL generated',
      data: response.data,
    };
  },

  getQueryHistory: async (
    page: number = 1,
    pageSize: number = 20,
    databaseMetadataId?: string
  ): Promise<ApiResponse<{ items: QueryHistoryItem[]; totalCount: number; totalPages: number }>> => {
    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
    });
    
    if (databaseMetadataId) {
      params.append('databaseMetadataId', databaseMetadataId);
    }

    const response = await apiClient.get(`/chatbot/history?${params}`);
    return {
      success: true,
      message: 'Query history retrieved',
      data: response.data,
    };
  },

  getRateLimitStatus: async (): Promise<ApiResponse<RateLimitStatus>> => {
    const response: AxiosResponse<RateLimitStatus> = await apiClient.get('/chatbot/rate-limit-status');
    return {
      success: true,
      message: 'Rate limit status retrieved',
      data: response.data,
    };
  },
};

// Database Metadata API
export const databaseApi = {
  getDatabaseMetadata: async (): Promise<ApiResponse<DatabaseMetadata[]>> => {
    const response: AxiosResponse<DatabaseMetadata[]> = await apiClient.get('/databasemetadata');
    return {
      success: true,
      message: 'Database metadata retrieved',
      data: response.data,
    };
  },

  getDatabaseMetadataById: async (id: string): Promise<ApiResponse<DatabaseMetadata>> => {
    const response: AxiosResponse<DatabaseMetadata> = await apiClient.get(`/databasemetadata/${id}`);
    return {
      success: true,
      message: 'Database metadata retrieved',
      data: response.data,
    };
  },

  createDatabaseMetadata: async (
    request: CreateDatabaseMetadataRequest
  ): Promise<ApiResponse<DatabaseMetadata>> => {
    const response: AxiosResponse<DatabaseMetadata> = await apiClient.post('/databasemetadata', request);
    return {
      success: true,
      message: 'Database metadata created',
      data: response.data,
    };
  },

  testConnection: async (request: TestConnectionRequest): Promise<ApiResponse<TestConnectionResult>> => {
    const response: AxiosResponse<TestConnectionResult> = await apiClient.post(
      '/databasemetadata/test-connection',
      request
    );
    return {
      success: true,
      message: 'Connection tested',
      data: response.data,
    };
  },
};

export default apiClient;
