using System.ComponentModel.DataAnnotations;

namespace VannaDotNet.WebApp.DTOs;

/// <summary>
/// Request to generate SQL from natural language
/// </summary>
public class GenerateSqlRequest
{
    [Required]
    public string Question { get; set; } = string.Empty;

    [Required]
    public Guid DatabaseConnectionId { get; set; }

    public GenerationOptions? Options { get; set; }

    public string CreatedBy { get; set; } = "webapp-user";
}

/// <summary>
/// Result of SQL generation
/// </summary>
public class GenerateSqlResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? GeneratedSql { get; set; }
    public double? Confidence { get; set; }
    public string? Explanation { get; set; }
    public List<string>? FollowUpQuestions { get; set; }
    public string? Error { get; set; }
    public Guid? SessionId { get; set; }
}

/// <summary>
/// Request to execute SQL query
/// </summary>
public class ExecuteSqlRequest
{
    [Required]
    public string SqlQuery { get; set; } = string.Empty;

    [Required]
    public Guid DatabaseConnectionId { get; set; }

    public string CreatedBy { get; set; } = "webapp-user";
}

/// <summary>
/// Result of SQL execution
/// </summary>
public class ExecuteSqlResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<Dictionary<string, object>>? Results { get; set; }
    public int? RowCount { get; set; }
    public TimeSpan? ExecutionTime { get; set; }
    public string? Error { get; set; }
    public List<ColumnInfo>? Columns { get; set; }
}

/// <summary>
/// Request to ask a question and get complete results
/// </summary>
public class AskQuestionRequest
{
    [Required]
    public string Question { get; set; } = string.Empty;

    [Required]
    public Guid DatabaseConnectionId { get; set; }

    public GenerationOptions? Options { get; set; }

    public string CreatedBy { get; set; } = "webapp-user";
}

/// <summary>
/// Complete result of asking a question
/// </summary>
public class AskQuestionResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Question { get; set; }
    public string? GeneratedSql { get; set; }
    public List<Dictionary<string, object>>? Results { get; set; }
    public double? Confidence { get; set; }
    public string? Explanation { get; set; }
    public List<string>? FollowUpQuestions { get; set; }
    public int? RowCount { get; set; }
    public TimeSpan? ExecutionTime { get; set; }
    public string? Error { get; set; }
    public Guid? SessionId { get; set; }
    public List<ColumnInfo>? Columns { get; set; }
}

/// <summary>
/// Request to test database connection
/// </summary>
public class TestConnectionRequest
{
    [Required]
    public string Host { get; set; } = string.Empty;

    [Required]
    public int Port { get; set; }

    [Required]
    public string DatabaseName { get; set; } = string.Empty;

    [Required]
    public string Username { get; set; } = string.Empty;

    [Required]
    public string Password { get; set; } = string.Empty;

    [Required]
    public string Type { get; set; } = string.Empty;
}

/// <summary>
/// Result of connection test
/// </summary>
public class TestConnectionResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Error { get; set; }
    public TimeSpan? ResponseTime { get; set; }
}

/// <summary>
/// Generation options for SQL generation
/// </summary>
public class GenerationOptions
{
    public double? Temperature { get; set; }
    public int? MaxTokens { get; set; }
    public bool IncludeExplanation { get; set; } = true;
    public bool IncludeFollowUpQuestions { get; set; } = true;
    public int MaxFollowUpQuestions { get; set; } = 5;
}

/// <summary>
/// Column information for query results
/// </summary>
public class ColumnInfo
{
    public string Name { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
    public bool IsNullable { get; set; }
}

/// <summary>
/// Database metadata DTOs
/// </summary>
public class DatabaseMetadataDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string DatabaseType { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<TableMetadataDto> Tables { get; set; } = new();
}

public class TableMetadataDto
{
    public Guid Id { get; set; }
    public string TableName { get; set; } = string.Empty;
    public string? SchemaName { get; set; }
    public string? Description { get; set; }
    public string? DisplayName { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<ColumnMetadataDto> Columns { get; set; } = new();
    public List<TableRelationshipDto> Relationships { get; set; } = new();
}

public class ColumnMetadataDto
{
    public Guid Id { get; set; }
    public string ColumnName { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
    public bool IsNullable { get; set; }
    public bool IsPrimaryKey { get; set; }
    public bool IsForeignKey { get; set; }
    public int? MaxLength { get; set; }
    public string? DefaultValue { get; set; }
    public List<CodeMappingDto> CodeMappings { get; set; } = new();
}

public class CodeMappingDto
{
    public Guid Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int SortOrder { get; set; }
    public bool IsActive { get; set; }
}

public class TableRelationshipDto
{
    public Guid Id { get; set; }
    public string ParentTable { get; set; } = string.Empty;
    public string ChildTable { get; set; } = string.Empty;
    public string ParentColumn { get; set; } = string.Empty;
    public string ChildColumn { get; set; } = string.Empty;
    public string RelationshipType { get; set; } = string.Empty;
    public string? Description { get; set; }
}

/// <summary>
/// Query history DTOs
/// </summary>
public class QueryHistoryDto
{
    public Guid Id { get; set; }
    public string Question { get; set; } = string.Empty;
    public string? GeneratedSql { get; set; }
    public bool IsSuccessful { get; set; }
    public string? ErrorMessage { get; set; }
    public double? Confidence { get; set; }
    public TimeSpan? ExecutionTime { get; set; }
    public int? RowCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public string DatabaseName { get; set; } = string.Empty;
}

/// <summary>
/// Create/Update database metadata request
/// </summary>
public class CreateDatabaseMetadataRequest
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    [MaxLength(1000)]
    public string? Description { get; set; }

    [Required]
    public string ConnectionString { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string DatabaseType { get; set; } = string.Empty;
}

/// <summary>
/// Create/Update table metadata request
/// </summary>
public class CreateTableMetadataRequest
{
    [Required]
    [MaxLength(200)]
    public string TableName { get; set; } = string.Empty;

    [MaxLength(100)]
    public string? SchemaName { get; set; }

    [MaxLength(1000)]
    public string? Description { get; set; }

    [MaxLength(200)]
    public string? DisplayName { get; set; }

    public List<CreateColumnMetadataRequest> Columns { get; set; } = new();
}

/// <summary>
/// Create/Update column metadata request
/// </summary>
public class CreateColumnMetadataRequest
{
    [Required]
    [MaxLength(200)]
    public string ColumnName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string DataType { get; set; } = string.Empty;

    [MaxLength(200)]
    public string? DisplayName { get; set; }

    [MaxLength(1000)]
    public string? Description { get; set; }

    public bool IsNullable { get; set; } = true;
    public bool IsPrimaryKey { get; set; } = false;
    public bool IsForeignKey { get; set; } = false;
    public int? MaxLength { get; set; }
    public string? DefaultValue { get; set; }

    public List<CreateCodeMappingRequest> CodeMappings { get; set; } = new();
}

/// <summary>
/// Create/Update code mapping request
/// </summary>
public class CreateCodeMappingRequest
{
    [Required]
    [MaxLength(100)]
    public string Code { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string Description { get; set; } = string.Empty;

    public int SortOrder { get; set; } = 0;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Chatbot request DTO
/// </summary>
public class ChatbotRequest
{
    [Required]
    [MaxLength(2000)]
    public string Question { get; set; } = string.Empty;

    [Required]
    public Guid DatabaseMetadataId { get; set; }

    public GenerationOptions? Options { get; set; }
}

/// <summary>
/// Chatbot response DTO
/// </summary>
public class ChatbotResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Question { get; set; }
    public string? GeneratedSql { get; set; }
    public List<Dictionary<string, object>>? Results { get; set; }
    public double? Confidence { get; set; }
    public string? Explanation { get; set; }
    public List<string>? FollowUpQuestions { get; set; }
    public int? RowCount { get; set; }
    public TimeSpan? ExecutionTime { get; set; }
    public string? Error { get; set; }
    public Guid? SessionId { get; set; }
    public List<ColumnInfo>? Columns { get; set; }
    public RateLimitInfo? RateLimitInfo { get; set; }
}

/// <summary>
/// Generate SQL request DTO
/// </summary>
public class GenerateSqlRequestDto
{
    [Required]
    [MaxLength(2000)]
    public string Question { get; set; } = string.Empty;

    [Required]
    public Guid DatabaseMetadataId { get; set; }

    public GenerationOptions? Options { get; set; }
}

/// <summary>
/// Generate SQL response DTO
/// </summary>
public class GenerateSqlResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? GeneratedSql { get; set; }
    public double? Confidence { get; set; }
    public string? Explanation { get; set; }
    public List<string>? FollowUpQuestions { get; set; }
    public string? Error { get; set; }
}

/// <summary>
/// Rate limit information DTO
/// </summary>
public class RateLimitInfo
{
    public int RequestCount { get; set; }
    public int MaxRequests { get; set; }
    public int RemainingRequests { get; set; }
    public DateTime ResetTime { get; set; }
}

/// <summary>
/// Paged result DTO
/// </summary>
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage => Page < TotalPages;
    public bool HasPreviousPage => Page > 1;
}
