{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=VannaDotNetWebApp;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Jwt": {"SecretKey": "your-super-secret-jwt-key-that-should-be-at-least-32-characters-long-for-production", "Issuer": "VannaDotNet.WebApp", "Audience": "VannaDotNet.WebApp.Users", "ExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "VannaDotNetApi": {"BaseUrl": "http://localhost:5008", "TimeoutSeconds": 30, "MaxRetries": 3, "ApiKey": null}, "RateLimit": {"TextToSqlMaxRequests": 100, "TextToSqlWindowHours": 1, "DatabaseMetadataMaxRequests": 50, "DatabaseMetadataWindowHours": 1, "CleanupIntervalMinutes": 60}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*"}