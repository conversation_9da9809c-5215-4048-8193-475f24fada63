# VannaDotNet Web Application

A comprehensive web application that provides a user-friendly interface for the VannaDotNet Text-to-SQL API system.

## Features

### 🔐 Authentication System
- User registration and login with JWT tokens
- Secure password hashing with BCrypt
- Profile management and user settings
- Automatic token refresh and session management

### 🤖 AI-Powered Chatbot Interface
- Natural language database queries
- Real-time SQL generation using VannaDotNet API
- Query execution with formatted results display
- Confidence scoring and explanations
- Follow-up question suggestions
- SQL syntax highlighting

### ⚡ Rate Limiting
- 100 queries per hour per user (configurable)
- Real-time rate limit status display
- Graceful handling when limits are exceeded
- Background cleanup of expired entries

### 🗄️ Database Metadata Management
- Define and manage database connections
- Table schema configuration
- Column metadata with data types
- Code/decode mappings for field values
- Relationship definitions between tables
- Connection testing functionality

### 📊 Query History & Analytics
- Complete query history with search and filtering
- Performance metrics and execution times
- Success/failure tracking
- Export capabilities (ready for implementation)

### 🎨 Modern Responsive UI
- Material-UI design system
- Mobile-friendly responsive layout
- Dark/light theme support (ready)
- Loading states and error handling
- Accessibility features

## Technology Stack

### Backend
- **Framework**: ASP.NET Core 9.0
- **Database**: Entity Framework Core with SQL Server
- **Authentication**: JWT Bearer tokens with ASP.NET Core Identity
- **API Documentation**: Swagger/OpenAPI
- **Rate Limiting**: Custom implementation with database persistence

### Frontend
- **Framework**: React 18 with TypeScript
- **UI Library**: Material-UI (MUI) v5
- **State Management**: React Query + Context API
- **Forms**: React Hook Form with Yup validation
- **Routing**: React Router v6
- **HTTP Client**: Axios with interceptors

## Getting Started

### Prerequisites
- .NET 9.0 SDK
- Node.js 16+ and npm
- SQL Server (LocalDB for development)
- VannaDotNet API running on http://localhost:5008

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd VannaDotNet/src/VannaDotNet.WebApp
   ```

2. **Install backend dependencies**
   ```bash
   dotnet restore
   ```

3. **Install frontend dependencies**
   ```bash
   cd ClientApp
   npm install
   cd ..
   ```

4. **Configure the application**
   - Update `appsettings.json` with your database connection string
   - Configure JWT settings and VannaDotNet API URL
   - Set up rate limiting parameters

5. **Run database migrations**
   ```bash
   dotnet ef database update
   ```

6. **Start the application**
   ```bash
   dotnet run
   ```

The application will be available at:
- **Web App**: https://localhost:5009
- **API Documentation**: https://localhost:5009/api-docs

## Configuration

### Database Connection
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=VannaDotNetWebApp;Trusted_Connection=true"
  }
}
```

### JWT Authentication
```json
{
  "Jwt": {
    "SecretKey": "your-secret-key-32-characters-minimum",
    "Issuer": "VannaDotNet.WebApp",
    "Audience": "VannaDotNet.WebApp.Users",
    "ExpirationMinutes": 60
  }
}
```

### VannaDotNet API Integration
```json
{
  "VannaDotNetApi": {
    "BaseUrl": "http://localhost:5008",
    "TimeoutSeconds": 30
  }
}
```

### Rate Limiting
```json
{
  "RateLimit": {
    "TextToSqlMaxRequests": 100,
    "TextToSqlWindowHours": 1,
    "DatabaseMetadataMaxRequests": 50,
    "DatabaseMetadataWindowHours": 1
  }
}
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Chatbot
- `POST /api/chatbot/ask` - Ask natural language question
- `POST /api/chatbot/generate-sql` - Generate SQL only
- `GET /api/chatbot/history` - Get query history
- `GET /api/chatbot/rate-limit-status` - Get rate limit status

### Database Metadata
- `GET /api/databasemetadata` - Get all user databases
- `POST /api/databasemetadata` - Create database metadata
- `GET /api/databasemetadata/{id}` - Get specific database
- `POST /api/databasemetadata/test-connection` - Test connection

## Development

### Project Structure
```
src/VannaDotNet.WebApp/
├── Controllers/           # API controllers
├── Services/             # Business logic services
├── Models/               # Data models and entities
├── Data/                 # Entity Framework context
├── DTOs/                 # Data transfer objects
├── ClientApp/            # React frontend
│   ├── src/
│   │   ├── components/   # React components
│   │   ├── pages/        # Page components
│   │   ├── services/     # API services
│   │   ├── types/        # TypeScript types
│   │   └── contexts/     # React contexts
│   └── public/           # Static assets
└── appsettings.json      # Configuration
```

### Running in Development

1. **Backend only**:
   ```bash
   dotnet run --no-launch-profile
   ```

2. **Frontend only**:
   ```bash
   cd ClientApp
   npm start
   ```

3. **Full application**:
   ```bash
   dotnet run
   ```

### Building for Production

```bash
dotnet publish -c Release -o ./publish
```

## Security Features

- JWT-based authentication with secure token handling
- Password hashing with BCrypt
- Rate limiting to prevent abuse
- CORS configuration for cross-origin requests
- Input validation and sanitization
- SQL injection prevention through parameterized queries
- XSS protection with proper output encoding

## Performance Optimizations

- Database indexing for common queries
- React Query for intelligent caching
- Lazy loading of components
- Background cleanup services
- Connection pooling
- Async/await throughout the application

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation in the `/docs` folder
- Review the API documentation at `/api-docs`
