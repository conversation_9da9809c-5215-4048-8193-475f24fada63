using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using VannaDotNet.WebApp.Models;

namespace VannaDotNet.WebApp.Data;

/// <summary>
/// Database context for the web application
/// </summary>
public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    // DbSets for custom entities
    public DbSet<UserDatabaseMetadata> UserDatabaseMetadata { get; set; }
    public DbSet<TableMetadata> TableMetadata { get; set; }
    public DbSet<ColumnMetadata> ColumnMetadata { get; set; }
    public DbSet<CodeMapping> CodeMappings { get; set; }
    public DbSet<TableRelationship> TableRelationships { get; set; }
    public DbSet<QueryHistory> QueryHistory { get; set; }
    public DbSet<RateLimitEntry> RateLimitEntries { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Configure UserDatabaseMetadata
        builder.Entity<UserDatabaseMetadata>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => new { e.UserId, e.Name }).IsUnique();

            entity.HasOne(e => e.User)
                .WithMany(u => u.DatabaseMetadata)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure TableMetadata
        builder.Entity<TableMetadata>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.DatabaseMetadataId);
            entity.HasIndex(e => new { e.DatabaseMetadataId, e.TableName, e.SchemaName }).IsUnique();

            entity.HasOne(e => e.DatabaseMetadata)
                .WithMany(d => d.Tables)
                .HasForeignKey(e => e.DatabaseMetadataId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure ColumnMetadata
        builder.Entity<ColumnMetadata>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TableMetadataId);
            entity.HasIndex(e => new { e.TableMetadataId, e.ColumnName }).IsUnique();

            entity.HasOne(e => e.Table)
                .WithMany(t => t.Columns)
                .HasForeignKey(e => e.TableMetadataId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure CodeMapping
        builder.Entity<CodeMapping>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.ColumnMetadataId);
            entity.HasIndex(e => new { e.ColumnMetadataId, e.Code }).IsUnique();

            entity.HasOne(e => e.Column)
                .WithMany(c => c.CodeMappings)
                .HasForeignKey(e => e.ColumnMetadataId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure TableRelationship
        builder.Entity<TableRelationship>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.ParentTableId);
            entity.HasIndex(e => e.ChildTableId);

            entity.HasOne(e => e.ParentTable)
                .WithMany(t => t.ParentRelationships)
                .HasForeignKey(e => e.ParentTableId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.ChildTable)
                .WithMany(t => t.ChildRelationships)
                .HasForeignKey(e => e.ChildTableId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // Configure QueryHistory
        builder.Entity<QueryHistory>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.DatabaseMetadataId);
            entity.HasIndex(e => e.CreatedAt);

            entity.HasOne(e => e.User)
                .WithMany(u => u.QueryHistory)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.DatabaseMetadata)
                .WithMany()
                .HasForeignKey(e => e.DatabaseMetadataId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure JSON column for Results
            entity.Property(e => e.Results)
                .HasColumnType("nvarchar(max)");

            // Configure TimeSpan conversion
            entity.Property(e => e.ExecutionTime)
                .HasConversion(
                    v => v.HasValue ? v.Value.TotalMilliseconds : (double?)null,
                    v => v.HasValue ? TimeSpan.FromMilliseconds(v.Value) : (TimeSpan?)null);
        });

        // Configure RateLimitEntry
        builder.Entity<RateLimitEntry>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => new { e.UserId, e.Action, e.Timestamp });
            entity.HasIndex(e => e.ExpiresAt);

            entity.HasOne(e => e.User)
                .WithMany(u => u.RateLimitEntries)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure ApplicationUser additional properties
        builder.Entity<ApplicationUser>(entity =>
        {
            entity.HasIndex(e => e.Email).IsUnique();
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => e.IsActive);
        });
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.Entity is UserDatabaseMetadata metadata)
            {
                if (entry.State == EntityState.Added)
                {
                    metadata.CreatedAt = DateTime.UtcNow;
                }
                metadata.UpdatedAt = DateTime.UtcNow;
            }
            else if (entry.Entity is TableMetadata table)
            {
                if (entry.State == EntityState.Added)
                {
                    table.CreatedAt = DateTime.UtcNow;
                }
                table.UpdatedAt = DateTime.UtcNow;
            }
            else if (entry.Entity is ColumnMetadata column)
            {
                if (entry.State == EntityState.Added)
                {
                    column.CreatedAt = DateTime.UtcNow;
                }
                column.UpdatedAt = DateTime.UtcNow;
            }
        }
    }
}
