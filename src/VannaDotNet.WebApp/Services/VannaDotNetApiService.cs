using System.Text;
using System.Text.Json;
using VannaDotNet.WebApp.DTOs;

namespace VannaDotNet.WebApp.Services;

/// <summary>
/// Service for communicating with the VannaDotNet API
/// </summary>
public interface IVannaDotNetApiService
{
    Task<GenerateSqlResult> GenerateSqlAsync(GenerateSqlRequest request);
    Task<ExecuteSqlResult> ExecuteSqlAsync(ExecuteSqlRequest request);
    Task<AskQuestionResult> AskQuestionAsync(AskQuestionRequest request);
    Task<bool> TestConnectionAsync(TestConnectionRequest request);
}

public class VannaDotNetApiService : IVannaDotNetApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<VannaDotNetApiService> _logger;
    private readonly IConfiguration _configuration;
    private readonly JsonSerializerOptions _jsonOptions;

    public VannaDotNetApiService(
        HttpClient httpClient,
        ILogger<VannaDotNetApiService> logger,
        IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configuration = configuration;

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        // Configure base address and default headers
        var baseUrl = _configuration["VannaDotNetApi:BaseUrl"] ?? "http://localhost:5008";
        _httpClient.BaseAddress = new Uri(baseUrl);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "VannaDotNet-WebApp/1.0");
    }

    public async Task<GenerateSqlResult> GenerateSqlAsync(GenerateSqlRequest request)
    {
        try
        {
            _logger.LogInformation("Generating SQL for question: {Question}", request.Question);

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/query/generate-sql", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<GenerateSqlResult>(responseContent, _jsonOptions);
                return result ?? new GenerateSqlResult
                {
                    Success = false,
                    Message = "Failed to deserialize response"
                };
            }
            else
            {
                _logger.LogWarning("SQL generation failed with status {StatusCode}: {Content}", 
                    response.StatusCode, responseContent);

                return new GenerateSqlResult
                {
                    Success = false,
                    Message = $"API request failed: {response.StatusCode}",
                    Error = responseContent
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating SQL for question: {Question}", request.Question);
            return new GenerateSqlResult
            {
                Success = false,
                Message = "An error occurred while generating SQL",
                Error = ex.Message
            };
        }
    }

    public async Task<ExecuteSqlResult> ExecuteSqlAsync(ExecuteSqlRequest request)
    {
        try
        {
            _logger.LogInformation("Executing SQL query");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/query/execute-sql", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ExecuteSqlResult>(responseContent, _jsonOptions);
                return result ?? new ExecuteSqlResult
                {
                    Success = false,
                    Message = "Failed to deserialize response"
                };
            }
            else
            {
                _logger.LogWarning("SQL execution failed with status {StatusCode}: {Content}", 
                    response.StatusCode, responseContent);

                return new ExecuteSqlResult
                {
                    Success = false,
                    Message = $"API request failed: {response.StatusCode}",
                    Error = responseContent
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing SQL query");
            return new ExecuteSqlResult
            {
                Success = false,
                Message = "An error occurred while executing SQL",
                Error = ex.Message
            };
        }
    }

    public async Task<AskQuestionResult> AskQuestionAsync(AskQuestionRequest request)
    {
        try
        {
            _logger.LogInformation("Processing question: {Question}", request.Question);

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/query/ask", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<AskQuestionResult>(responseContent, _jsonOptions);
                return result ?? new AskQuestionResult
                {
                    Success = false,
                    Message = "Failed to deserialize response"
                };
            }
            else
            {
                _logger.LogWarning("Question processing failed with status {StatusCode}: {Content}", 
                    response.StatusCode, responseContent);

                return new AskQuestionResult
                {
                    Success = false,
                    Message = $"API request failed: {response.StatusCode}",
                    Error = responseContent
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing question: {Question}", request.Question);
            return new AskQuestionResult
            {
                Success = false,
                Message = "An error occurred while processing the question",
                Error = ex.Message
            };
        }
    }

    public async Task<bool> TestConnectionAsync(TestConnectionRequest request)
    {
        try
        {
            _logger.LogInformation("Testing database connection");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/connections/test", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<TestConnectionResult>(responseContent, _jsonOptions);
                return result?.Success ?? false;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing database connection");
            return false;
        }
    }
}

/// <summary>
/// Configuration for VannaDotNet API
/// </summary>
public class VannaDotNetApiConfiguration
{
    public const string SectionName = "VannaDotNetApi";

    public string BaseUrl { get; set; } = "http://localhost:5008";
    public int TimeoutSeconds { get; set; } = 30;
    public int MaxRetries { get; set; } = 3;
    public string? ApiKey { get; set; }
}
