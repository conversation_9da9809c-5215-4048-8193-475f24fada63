using Microsoft.EntityFrameworkCore;
using VannaDotNet.WebApp.Data;
using VannaDotNet.WebApp.Models;

namespace VannaDotNet.WebApp.Services;

/// <summary>
/// Rate limiting service for controlling user actions
/// </summary>
public interface IRateLimitService
{
    Task<bool> IsAllowedAsync(string userId, string action, int maxRequests = 100, TimeSpan? window = null);
    Task RecordActionAsync(string userId, string action, TimeSpan? expiration = null);
    Task<RateLimitStatus> GetStatusAsync(string userId, string action, int maxRequests = 100, TimeSpan? window = null);
    Task CleanupExpiredEntriesAsync();
}

public class RateLimitService : IRateLimitService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<RateLimitService> _logger;
    private readonly IConfiguration _configuration;

    public RateLimitService(
        ApplicationDbContext context,
        ILogger<RateLimitService> logger,
        IConfiguration configuration)
    {
        _context = context;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<bool> IsAllowedAsync(string userId, string action, int maxRequests = 100, TimeSpan? window = null)
    {
        try
        {
            var windowDuration = window ?? TimeSpan.FromHours(1);
            var cutoffTime = DateTime.UtcNow - windowDuration;

            var requestCount = await _context.RateLimitEntries
                .CountAsync(r => r.UserId == userId && 
                                r.Action == action && 
                                r.Timestamp >= cutoffTime);

            return requestCount < maxRequests;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking rate limit for user {UserId} and action {Action}", userId, action);
            // In case of error, allow the request to avoid blocking users
            return true;
        }
    }

    public async Task RecordActionAsync(string userId, string action, TimeSpan? expiration = null)
    {
        try
        {
            var expirationTime = expiration ?? TimeSpan.FromHours(1);
            var entry = new RateLimitEntry
            {
                UserId = userId,
                Action = action,
                Timestamp = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.Add(expirationTime)
            };

            _context.RateLimitEntries.Add(entry);
            await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording rate limit entry for user {UserId} and action {Action}", userId, action);
            // Don't throw exception to avoid blocking the user's request
        }
    }

    public async Task<RateLimitStatus> GetStatusAsync(string userId, string action, int maxRequests = 100, TimeSpan? window = null)
    {
        try
        {
            var windowDuration = window ?? TimeSpan.FromHours(1);
            var cutoffTime = DateTime.UtcNow - windowDuration;

            var entries = await _context.RateLimitEntries
                .Where(r => r.UserId == userId && 
                           r.Action == action && 
                           r.Timestamp >= cutoffTime)
                .OrderByDescending(r => r.Timestamp)
                .ToListAsync();

            var requestCount = entries.Count;
            var remainingRequests = Math.Max(0, maxRequests - requestCount);
            var resetTime = entries.Any() ? entries.First().Timestamp.Add(windowDuration) : DateTime.UtcNow.Add(windowDuration);

            return new RateLimitStatus
            {
                IsAllowed = requestCount < maxRequests,
                RequestCount = requestCount,
                MaxRequests = maxRequests,
                RemainingRequests = remainingRequests,
                ResetTime = resetTime,
                WindowDuration = windowDuration
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rate limit status for user {UserId} and action {Action}", userId, action);
            
            // Return a permissive status in case of error
            return new RateLimitStatus
            {
                IsAllowed = true,
                RequestCount = 0,
                MaxRequests = maxRequests,
                RemainingRequests = maxRequests,
                ResetTime = DateTime.UtcNow.Add(window ?? TimeSpan.FromHours(1)),
                WindowDuration = window ?? TimeSpan.FromHours(1)
            };
        }
    }

    public async Task CleanupExpiredEntriesAsync()
    {
        try
        {
            var cutoffTime = DateTime.UtcNow;
            var expiredEntries = await _context.RateLimitEntries
                .Where(r => r.ExpiresAt < cutoffTime)
                .ToListAsync();

            if (expiredEntries.Any())
            {
                _context.RateLimitEntries.RemoveRange(expiredEntries);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Cleaned up {Count} expired rate limit entries", expiredEntries.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired rate limit entries");
        }
    }
}

/// <summary>
/// Rate limit status information
/// </summary>
public class RateLimitStatus
{
    public bool IsAllowed { get; set; }
    public int RequestCount { get; set; }
    public int MaxRequests { get; set; }
    public int RemainingRequests { get; set; }
    public DateTime ResetTime { get; set; }
    public TimeSpan WindowDuration { get; set; }
}

/// <summary>
/// Rate limit configuration
/// </summary>
public class RateLimitConfiguration
{
    public const string SectionName = "RateLimit";

    public int TextToSqlMaxRequests { get; set; } = 100;
    public int TextToSqlWindowHours { get; set; } = 1;
    public int DatabaseMetadataMaxRequests { get; set; } = 50;
    public int DatabaseMetadataWindowHours { get; set; } = 1;
    public int CleanupIntervalMinutes { get; set; } = 60;
}

/// <summary>
/// Background service for cleaning up expired rate limit entries
/// </summary>
public class RateLimitCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RateLimitCleanupService> _logger;
    private readonly IConfiguration _configuration;

    public RateLimitCleanupService(
        IServiceProvider serviceProvider,
        ILogger<RateLimitCleanupService> logger,
        IConfiguration configuration)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _configuration = configuration;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var rateLimitConfig = _configuration.GetSection(RateLimitConfiguration.SectionName)
            .Get<RateLimitConfiguration>() ?? new RateLimitConfiguration();

        var interval = TimeSpan.FromMinutes(rateLimitConfig.CleanupIntervalMinutes);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var rateLimitService = scope.ServiceProvider.GetRequiredService<IRateLimitService>();
                
                await rateLimitService.CleanupExpiredEntriesAsync();
                
                await Task.Delay(interval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in rate limit cleanup service");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Wait 5 minutes before retrying
            }
        }
    }
}
