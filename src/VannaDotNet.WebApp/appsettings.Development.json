{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=VannaDotNetWebApp_Dev;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Jwt": {"SecretKey": "development-jwt-secret-key-32-characters-minimum-length-required", "Issuer": "VannaDotNet.WebApp.Dev", "Audience": "VannaDotNet.WebApp.Dev.Users", "ExpirationMinutes": 120, "RefreshTokenExpirationDays": 30}, "VannaDotNetApi": {"BaseUrl": "http://localhost:5008", "TimeoutSeconds": 60, "MaxRetries": 3, "ApiKey": null}, "RateLimit": {"TextToSqlMaxRequests": 1000, "TextToSqlWindowHours": 1, "DatabaseMetadataMaxRequests": 100, "DatabaseMetadataWindowHours": 1, "CleanupIntervalMinutes": 30}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information", "VannaDotNet.WebApp": "Debug"}}}