using System.Collections.Generic;
using MediatR;
using VannaDotNet.Application.Common.Models;

namespace VannaDotNet.Application.Features.Training.Commands.AddDocumentationTrainingData;

/// <summary>
/// Command to add documentation training data
/// </summary>
public record AddDocumentationTrainingDataCommand(
    string Documentation,
    string? Description,
    List<string>? Tags,
    string? Category,
    string CreatedBy = "system"
) : IRequest<TrainingDataResponse>;
