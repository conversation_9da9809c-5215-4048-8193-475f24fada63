using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using VannaDotNet.Application.Common.Interfaces;
using VannaDotNet.Application.Common.Models;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Repositories;

namespace VannaDotNet.Application.Features.Training.Commands.AddDocumentationTrainingData;

/// <summary>
/// Handler for AddDocumentationTrainingDataCommand
/// </summary>
public class AddDocumentationTrainingDataCommandHandler : IRequestHandler<AddDocumentationTrainingDataCommand, TrainingDataResponse>
{
    private readonly ITrainingDataRepository _trainingDataRepository;
    private readonly IVectorStoreService _vectorStoreService;
    private readonly IEmbeddingService _embeddingService;
    private readonly ILogger<AddDocumentationTrainingDataCommandHandler> _logger;

    public AddDocumentationTrainingDataCommandHandler(
        ITrainingDataRepository trainingDataRepository,
        IVectorStoreService vectorStoreService,
        IEmbeddingService embeddingService,
        ILogger<AddDocumentationTrainingDataCommandHandler> logger)
    {
        _trainingDataRepository = trainingDataRepository;
        _vectorStoreService = vectorStoreService;
        _embeddingService = embeddingService;
        _logger = logger;
    }

    public async Task<TrainingDataResponse> Handle(AddDocumentationTrainingDataCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Adding documentation training data: {Documentation}", 
            request.Documentation.Length > 100 ? request.Documentation[..100] + "..." : request.Documentation);

        try
        {
            // Check if similar documentation already exists
            var existingData = await _trainingDataRepository.ExistsAsync(
                request.Documentation, 
                Domain.Enums.TrainingDataType.Documentation, 
                cancellationToken);

            if (existingData)
            {
                _logger.LogWarning("Documentation training data already exists");
                return new TrainingDataResponse
                {
                    Id = Guid.Empty,
                    Type = Domain.Enums.TrainingDataType.Documentation,
                    Status = "Duplicate",
                    Message = "Similar documentation training data already exists"
                };
            }

            // Prepare tags including category
            var tags = new List<string>(request.Tags ?? new List<string>());
            if (!string.IsNullOrEmpty(request.Category))
            {
                tags.Add($"category:{request.Category}");
            }

            // Create training data entity
            var trainingData = TrainingData.CreateDocumentation(
                request.Documentation,
                request.Description,
                tags,
                request.CreatedBy);

            // Generate embedding for the documentation
            var embedding = await _embeddingService.GenerateEmbeddingAsync(request.Documentation, cancellationToken);

            // Store in vector database
            await _vectorStoreService.StoreEmbeddingAsync(
                trainingData.Id,
                embedding,
                request.Documentation,
                new Dictionary<string, object>
                {
                    ["type"] = "documentation",
                    ["category"] = request.Category ?? "",
                    ["tags"] = string.Join(",", tags)
                },
                cancellationToken);

            // Store in primary database
            await _trainingDataRepository.AddAsync(trainingData, cancellationToken);

            _logger.LogInformation("Successfully added documentation training data with ID: {Id}", trainingData.Id);

            return new TrainingDataResponse
            {
                Id = trainingData.Id,
                Type = Domain.Enums.TrainingDataType.Documentation,
                Status = "Success",
                Message = "Documentation training data added successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding documentation training data");
            
            return new TrainingDataResponse
            {
                Id = Guid.Empty,
                Type = Domain.Enums.TrainingDataType.Documentation,
                Status = "Error",
                Message = $"Failed to add documentation training data: {ex.Message}"
            };
        }
    }
}
