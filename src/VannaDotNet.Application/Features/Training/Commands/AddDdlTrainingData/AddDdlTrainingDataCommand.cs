using System.Collections.Generic;
using MediatR;
using VannaDotNet.Application.Common.Models;

namespace VannaDotNet.Application.Features.Training.Commands.AddDdlTrainingData;

/// <summary>
/// Command to add DDL training data
/// </summary>
public record AddDdlTrainingDataCommand(
    string Ddl,
    string? Description,
    List<string>? Tags,
    string? SchemaName,
    string? TableName,
    string CreatedBy = "system"
) : IRequest<TrainingDataResponse>;
