using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using VannaDotNet.Application.Common.Interfaces;
using VannaDotNet.Application.Common.Models;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Repositories;

namespace VannaDotNet.Application.Features.Training.Commands.AddDdlTrainingData;

/// <summary>
/// Handler for AddDdlTrainingDataCommand
/// </summary>
public class AddDdlTrainingDataCommandHandler : IRequestHandler<AddDdlTrainingDataCommand, TrainingDataResponse>
{
    private readonly ITrainingDataRepository _trainingDataRepository;
    private readonly IVectorStoreService _vectorStoreService;
    private readonly IEmbeddingService _embeddingService;
    private readonly ILogger<AddDdlTrainingDataCommandHandler> _logger;

    public AddDdlTrainingDataCommandHandler(
        ITrainingDataRepository trainingDataRepository,
        IVectorStoreService vectorStoreService,
        IEmbeddingService embeddingService,
        ILogger<AddDdlTrainingDataCommandHandler> logger)
    {
        _trainingDataRepository = trainingDataRepository;
        _vectorStoreService = vectorStoreService;
        _embeddingService = embeddingService;
        _logger = logger;
    }

    public async Task<TrainingDataResponse> Handle(AddDdlTrainingDataCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Adding DDL training data: {Ddl}", request.Ddl);

        try
        {
            // Check if similar DDL already exists
            var existingData = await _trainingDataRepository.ExistsAsync(
                request.Ddl, 
                Domain.Enums.TrainingDataType.Ddl, 
                cancellationToken);

            if (existingData)
            {
                _logger.LogWarning("DDL training data already exists: {Ddl}", request.Ddl);
                return new TrainingDataResponse
                {
                    Id = Guid.Empty,
                    Type = Domain.Enums.TrainingDataType.Ddl,
                    Status = "Duplicate",
                    Message = "Similar DDL training data already exists"
                };
            }

            // Create training data entity
            var trainingData = TrainingData.CreateDdl(
                request.Ddl,
                request.Description,
                request.Tags,
                request.SchemaName,
                request.TableName,
                request.CreatedBy);

            // Generate embedding for the DDL
            var embedding = await _embeddingService.GenerateEmbeddingAsync(request.Ddl, cancellationToken);

            // Store in vector database
            await _vectorStoreService.StoreEmbeddingAsync(
                trainingData.Id,
                embedding,
                request.Ddl,
                new System.Collections.Generic.Dictionary<string, object>
                {
                    ["type"] = "ddl",
                    ["schema"] = request.SchemaName ?? "",
                    ["table"] = request.TableName ?? "",
                    ["tags"] = string.Join(",", request.Tags ?? new List<string>())
                },
                cancellationToken);

            // Store in primary database
            await _trainingDataRepository.AddAsync(trainingData, cancellationToken);

            _logger.LogInformation("Successfully added DDL training data with ID: {Id}", trainingData.Id);

            return new TrainingDataResponse
            {
                Id = trainingData.Id,
                Type = Domain.Enums.TrainingDataType.Ddl,
                Status = "Success",
                Message = "DDL training data added successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding DDL training data: {Ddl}", request.Ddl);
            
            return new TrainingDataResponse
            {
                Id = Guid.Empty,
                Type = Domain.Enums.TrainingDataType.Ddl,
                Status = "Error",
                Message = $"Failed to add DDL training data: {ex.Message}"
            };
        }
    }
}
