using FluentValidation;

namespace VannaDotNet.Application.Features.Training.Commands.AddDdlTrainingData;

/// <summary>
/// Validator for AddDdlTrainingDataCommand
/// </summary>
public class AddDdlTrainingDataCommandValidator : AbstractValidator<AddDdlTrainingDataCommand>
{
    public AddDdlTrainingDataCommandValidator()
    {
        RuleFor(x => x.Ddl)
            .NotEmpty()
            .WithMessage("DDL statement is required")
            .MaximumLength(10000)
            .WithMessage("DDL statement cannot exceed 10,000 characters")
            .Must(BeValidDdl)
            .WithMessage("DDL statement must be a valid CREATE, ALTER, or DROP statement");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Description cannot exceed 1,000 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.SchemaName)
            .MaximumLength(100)
            .WithMessage("Schema name cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.SchemaName));

        RuleFor(x => x.TableName)
            .MaximumLength(100)
            .WithMessage("Table name cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.TableName));

        RuleFor(x => x.Tags)
            .Must(HaveValidTags)
            .WithMessage("Tags must be non-empty and not exceed 50 characters each")
            .When(x => x.Tags != null && x.Tags.Count > 0);

        RuleFor(x => x.CreatedBy)
            .NotEmpty()
            .WithMessage("CreatedBy is required")
            .MaximumLength(100)
            .WithMessage("CreatedBy cannot exceed 100 characters");
    }

    private static bool BeValidDdl(string ddl)
    {
        if (string.IsNullOrWhiteSpace(ddl))
            return false;

        var upperDdl = ddl.Trim().ToUpperInvariant();
        
        return upperDdl.StartsWith("CREATE") ||
               upperDdl.StartsWith("ALTER") ||
               upperDdl.StartsWith("DROP");
    }

    private static bool HaveValidTags(System.Collections.Generic.List<string>? tags)
    {
        if (tags == null || tags.Count == 0)
            return true;

        foreach (var tag in tags)
        {
            if (string.IsNullOrWhiteSpace(tag) || tag.Length > 50)
                return false;
        }

        return true;
    }
}
