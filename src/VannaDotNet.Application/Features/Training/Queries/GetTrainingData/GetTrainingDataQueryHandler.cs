using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VannaDotNet.Application.Common.Models;
using VannaDotNet.Domain.Repositories;

namespace VannaDotNet.Application.Features.Training.Queries.GetTrainingData;

/// <summary>
/// Handler for GetTrainingDataQuery
/// </summary>
public class GetTrainingDataQueryHandler : IRequestHandler<GetTrainingDataQuery, PagedTrainingDataResult>
{
    private readonly ITrainingDataRepository _trainingDataRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetTrainingDataQueryHandler> _logger;

    public GetTrainingDataQueryHandler(
        ITrainingDataRepository trainingDataRepository,
        IMapper mapper,
        ILogger<GetTrainingDataQueryHandler> logger)
    {
        _trainingDataRepository = trainingDataRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedTrainingDataResult> Handle(GetTrainingDataQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting training data - Page: {Page}, PageSize: {PageSize}, Type: {Type}", 
            request.Page, request.PageSize, request.Type);

        // Get paginated training data from repository
        var (items, totalCount) = await _trainingDataRepository.GetPaginatedAsync(
            request.Page,
            request.PageSize,
            request.Type,
            request.IsActive,
            request.Tags,
            request.SearchTerm,
            cancellationToken);

        // Map entities to DTOs
        var trainingDataDtos = items.Select(_mapper.Map<TrainingDataDto>).ToList();

        _logger.LogInformation("Retrieved {Count} training data items out of {Total} total", 
            trainingDataDtos.Count, totalCount);

        return new PagedTrainingDataResult
        {
            Items = trainingDataDtos,
            Page = request.Page,
            PageSize = request.PageSize,
            TotalCount = totalCount
        };
    }
}
