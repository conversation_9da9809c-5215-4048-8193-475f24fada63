using System.Collections.Generic;
using MediatR;
using VannaDotNet.Application.Common.Models;
using VannaDotNet.Domain.Enums;

namespace VannaDotNet.Application.Features.Training.Queries.GetTrainingData;

/// <summary>
/// Query to get training data with filtering and pagination
/// </summary>
public record GetTrainingDataQuery(
    int Page = 1,
    int PageSize = 20,
    TrainingDataType? Type = null,
    bool? IsActive = null,
    List<string>? Tags = null,
    string? SearchTerm = null,
    string? SchemaName = null,
    string? TableName = null
) : IRequest<PagedTrainingDataResult>;
