using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using VannaDotNet.Application.Common.Interfaces;
using VannaDotNet.Application.Common.Models;
using VannaDotNet.Application.Services;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Repositories;
using VannaDotNet.Domain.ValueObjects;

namespace VannaDotNet.Application.Features.Query.Commands.GenerateSql;

/// <summary>
/// Handler for GenerateSqlCommand
/// </summary>
public class GenerateSqlCommandHandler : IRequestHandler<GenerateSqlCommand, GenerateSqlResponse>
{
    private readonly IQuerySessionRepository _querySessionRepository;
    private readonly IDatabaseConnectionRepository _databaseConnectionRepository;
    private readonly IVectorStoreService _vectorStoreService;
    private readonly ILlmService _llmService;
    private readonly IEmbeddingService _embeddingService;
    private readonly IPromptService _promptService;
    private readonly ILogger<GenerateSqlCommandHandler> _logger;

    public GenerateSqlCommandHandler(
        IQuerySessionRepository querySessionRepository,
        IDatabaseConnectionRepository databaseConnectionRepository,
        IVectorStoreService vectorStoreService,
        ILlmService llmService,
        IEmbeddingService embeddingService,
        IPromptService promptService,
        ILogger<GenerateSqlCommandHandler> logger)
    {
        _querySessionRepository = querySessionRepository;
        _databaseConnectionRepository = databaseConnectionRepository;
        _vectorStoreService = vectorStoreService;
        _llmService = llmService;
        _embeddingService = embeddingService;
        _promptService = promptService;
        _logger = logger;
    }

    public async Task<GenerateSqlResponse> Handle(GenerateSqlCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        _logger.LogInformation("Generating SQL for question: {Question}", request.Question);

        try
        {
            // Validate database connection exists and is active
            var dbConnection = await _databaseConnectionRepository.GetByIdAsync(request.DatabaseConnectionId, cancellationToken);
            if (dbConnection == null || !dbConnection.IsActive)
            {
                throw new InvalidOperationException("Database connection not found or inactive");
            }

            // Create query session
            var querySession = QuerySession.Create(request.Question, request.DatabaseConnectionId, request.CreatedBy);
            await _querySessionRepository.AddAsync(querySession, cancellationToken);

            // Start generation
            querySession.StartGeneration();
            await _querySessionRepository.UpdateAsync(querySession, cancellationToken);

            // Step 1: Retrieve similar training data using RAG
            var similarData = await RetrieveSimilarTrainingDataAsync(request.Question, cancellationToken);

            // Step 2: Build context prompt
            var prompt = await _promptService.BuildSqlGenerationPromptAsync(
                request.Question,
                similarData,
                dbConnection,
                request.Options,
                cancellationToken);

            // Step 3: Generate SQL using LLM
            var llmOptions = new LlmOptions
            {
                Model = request.Options?.Model,
                Temperature = request.Options?.Temperature ?? 0.1,
                MaxTokens = request.Options?.MaxTokens ?? 4000
            };

            var llmResponse = await _llmService.GenerateCompletionAsync(prompt, llmOptions, cancellationToken);

            // Step 4: Extract and validate SQL
            var sql = ExtractSqlFromResponse(llmResponse);
            var sqlQuery = new SqlQuery(sql); // This validates the SQL

            // Step 5: Calculate confidence score
            var confidence = CalculateConfidenceScore(llmResponse, similarData, sqlQuery);

            // Step 6: Generate explanation and follow-up questions (if requested)
            string? explanation = null;
            List<string> followUpQuestions = new();

            if (request.Options?.IncludeExplanation == true)
            {
                explanation = await _llmService.GenerateExplanationAsync(request.Question, sql, cancellationToken);
            }

            if (request.Options?.GenerateFollowUpQuestions == true)
            {
                var followUps = await _llmService.GenerateFollowUpQuestionsAsync(
                    request.Question, 
                    sql, 
                    count: request.Options.FollowUpQuestionCount,
                    cancellationToken: cancellationToken);
                followUpQuestions = followUps.ToList();
            }

            // Complete generation
            querySession.CompleteGeneration(sql, confidence, explanation, followUpQuestions);
            await _querySessionRepository.UpdateAsync(querySession, cancellationToken);

            stopwatch.Stop();

            _logger.LogInformation("Successfully generated SQL for question: {Question} in {ElapsedMs}ms", 
                request.Question, stopwatch.ElapsedMilliseconds);

            return new GenerateSqlResponse
            {
                Sql = sql,
                Confidence = confidence,
                Explanation = explanation,
                FollowUpQuestions = followUpQuestions,
                GenerationTime = stopwatch.Elapsed,
                SessionId = querySession.Id
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error generating SQL for question: {Question}", request.Question);
            
            throw;
        }
    }

    private async Task<List<SimilarityResult>> RetrieveSimilarTrainingDataAsync(
        string question, 
        CancellationToken cancellationToken)
    {
        try
        {
            // Generate embedding for the question
            var questionEmbedding = await _embeddingService.GenerateEmbeddingAsync(question, cancellationToken);

            // Search for similar training data
            var similarData = await _vectorStoreService.SearchSimilarAsync(
                questionEmbedding,
                limit: 10,
                minScore: 0.7,
                cancellationToken: cancellationToken);

            return similarData.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve similar training data, proceeding without context");
            return new List<SimilarityResult>();
        }
    }

    private static string ExtractSqlFromResponse(string llmResponse)
    {
        // Extract SQL from LLM response
        // Look for SQL code blocks or common SQL patterns
        var lines = llmResponse.Split('\n');
        var sqlLines = new List<string>();
        bool inSqlBlock = false;

        foreach (var line in lines)
        {
            var trimmedLine = line.Trim();
            
            // Check for SQL code block markers
            if (trimmedLine.StartsWith("```sql", StringComparison.OrdinalIgnoreCase) ||
                trimmedLine.StartsWith("```SQL", StringComparison.OrdinalIgnoreCase))
            {
                inSqlBlock = true;
                continue;
            }
            
            if (trimmedLine == "```" && inSqlBlock)
            {
                inSqlBlock = false;
                break;
            }
            
            if (inSqlBlock)
            {
                sqlLines.Add(line);
                continue;
            }
            
            // Check for SQL keywords at the start of lines
            if (trimmedLine.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase) ||
                trimmedLine.StartsWith("WITH", StringComparison.OrdinalIgnoreCase))
            {
                sqlLines.Add(line);
                inSqlBlock = true;
                continue;
            }
            
            if (inSqlBlock && !string.IsNullOrWhiteSpace(trimmedLine))
            {
                sqlLines.Add(line);
            }
            else if (inSqlBlock && string.IsNullOrWhiteSpace(trimmedLine))
            {
                // Empty line might indicate end of SQL
                break;
            }
        }

        var sql = string.Join('\n', sqlLines).Trim();
        
        if (string.IsNullOrEmpty(sql))
        {
            // Fallback: try to extract any SQL-like content
            sql = llmResponse.Trim();
        }

        return sql;
    }

    private static double CalculateConfidenceScore(
        string llmResponse, 
        List<SimilarityResult> similarData, 
        SqlQuery sqlQuery)
    {
        double confidence = 0.5; // Base confidence

        // Factor 1: Quality of similar training data
        if (similarData.Any())
        {
            var avgSimilarity = similarData.Average(x => x.Score);
            confidence += avgSimilarity * 0.3;
        }

        // Factor 2: SQL complexity (simpler queries are more likely to be correct)
        var complexityFactor = Math.Max(0, 1.0 - (sqlQuery.ComplexityScore / 20.0));
        confidence += complexityFactor * 0.1;

        // Factor 3: Response contains SQL keywords
        var upperResponse = llmResponse.ToUpperInvariant();
        var sqlKeywords = new[] { "SELECT", "FROM", "WHERE", "JOIN", "GROUP BY", "ORDER BY" };
        var keywordCount = sqlKeywords.Count(keyword => upperResponse.Contains(keyword));
        confidence += (keywordCount / (double)sqlKeywords.Length) * 0.1;

        // Ensure confidence is between 0 and 1
        return Math.Max(0.0, Math.Min(1.0, confidence));
    }
}
