using System;
using MediatR;
using VannaDotNet.Application.Common.Models;

namespace VannaDotNet.Application.Features.Query.Commands.GenerateSql;

/// <summary>
/// Command to generate SQL from a natural language question
/// </summary>
public record GenerateSqlCommand(
    string Question,
    Guid DatabaseConnectionId,
    GenerationOptions? Options = null,
    string CreatedBy = "system"
) : IRequest<GenerateSqlResponse>;
