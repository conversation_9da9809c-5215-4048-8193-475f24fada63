using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using VannaDotNet.Application.Common.Interfaces;
using VannaDotNet.Application.Common.Models;
using VannaDotNet.Domain.Entities;

namespace VannaDotNet.Application.Services;

/// <summary>
/// Service for building prompts for LLM interactions
/// </summary>
public interface IPromptService
{
    /// <summary>
    /// Builds a prompt for SQL generation
    /// </summary>
    Task<string> BuildSqlGenerationPromptAsync(
        string question,
        IEnumerable<SimilarityResult> similarData,
        DatabaseConnection databaseConnection,
        GenerationOptions? options = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Builds a prompt for SQL explanation
    /// </summary>
    Task<string> BuildExplanationPromptAsync(
        string question,
        string sql,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Builds a prompt for generating follow-up questions
    /// </summary>
    Task<string> BuildFollowUpQuestionsPromptAsync(
        string question,
        string sql,
        object? results = null,
        int count = 5,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Builds a prompt for generating a question from SQL
    /// </summary>
    Task<string> BuildQuestionFromSqlPromptAsync(
        string sql,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Builds a system prompt for the LLM
    /// </summary>
    string BuildSystemPrompt(DatabaseConnection databaseConnection);

    /// <summary>
    /// Formats training data for inclusion in prompts
    /// </summary>
    string FormatTrainingDataForPrompt(IEnumerable<SimilarityResult> similarData);
}
