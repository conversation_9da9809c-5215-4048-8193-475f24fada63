using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using VannaDotNet.Application.Common.Interfaces;
using VannaDotNet.Application.Common.Models;
using VannaDotNet.Domain.Entities;

namespace VannaDotNet.Application.Services;

/// <summary>
/// Implementation of prompt service for building LLM prompts
/// </summary>
public class PromptService : IPromptService
{
    public Task<string> BuildSqlGenerationPromptAsync(
        string question,
        IEnumerable<SimilarityResult> similarData,
        DatabaseConnection databaseConnection,
        GenerationOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        var prompt = new StringBuilder();

        // System prompt
        prompt.AppendLine(BuildSystemPrompt(databaseConnection));
        prompt.AppendLine();

        // Context from similar training data
        var formattedTrainingData = FormatTrainingDataForPrompt(similarData);
        if (!string.IsNullOrEmpty(formattedTrainingData))
        {
            prompt.AppendLine("## Relevant Context");
            prompt.AppendLine("Here are some relevant examples and documentation:");
            prompt.AppendLine();
            prompt.AppendLine(formattedTrainingData);
            prompt.AppendLine();
        }

        // Database-specific information
        prompt.AppendLine("## Database Information");
        prompt.AppendLine($"Database Type: {databaseConnection.Type}");
        prompt.AppendLine($"Database Name: {databaseConnection.DatabaseName}");
        
        if (databaseConnection.AllowedSchemas.Any())
        {
            prompt.AppendLine($"Available Schemas: {string.Join(", ", databaseConnection.AllowedSchemas)}");
        }
        prompt.AppendLine();

        // Instructions
        prompt.AppendLine("## Instructions");
        prompt.AppendLine("1. Generate a SQL query that answers the user's question");
        prompt.AppendLine("2. Use only SELECT statements (no INSERT, UPDATE, DELETE, or DDL)");
        prompt.AppendLine("3. Follow the database-specific SQL syntax");
        prompt.AppendLine("4. Use proper table and column names based on the context");
        prompt.AppendLine("5. Include appropriate WHERE clauses, JOINs, and aggregations as needed");
        prompt.AppendLine("6. Optimize for readability and performance");
        prompt.AppendLine("7. Return only the SQL query without explanations");
        prompt.AppendLine();

        // The actual question
        prompt.AppendLine("## Question");
        prompt.AppendLine(question);
        prompt.AppendLine();

        prompt.AppendLine("## SQL Query");
        prompt.AppendLine("```sql");

        return Task.FromResult(prompt.ToString());
    }

    public Task<string> BuildExplanationPromptAsync(
        string question,
        string sql,
        CancellationToken cancellationToken = default)
    {
        var prompt = new StringBuilder();

        prompt.AppendLine("You are a SQL expert. Explain the following SQL query in simple terms.");
        prompt.AppendLine();
        prompt.AppendLine("## Original Question");
        prompt.AppendLine(question);
        prompt.AppendLine();
        prompt.AppendLine("## SQL Query");
        prompt.AppendLine("```sql");
        prompt.AppendLine(sql);
        prompt.AppendLine("```");
        prompt.AppendLine();
        prompt.AppendLine("## Instructions");
        prompt.AppendLine("Provide a clear, concise explanation of:");
        prompt.AppendLine("1. What the query does");
        prompt.AppendLine("2. Which tables it accesses");
        prompt.AppendLine("3. What conditions it applies");
        prompt.AppendLine("4. How it processes the data");
        prompt.AppendLine("5. What the expected output format is");
        prompt.AppendLine();
        prompt.AppendLine("Keep the explanation accessible to non-technical users.");

        return Task.FromResult(prompt.ToString());
    }

    public Task<string> BuildFollowUpQuestionsPromptAsync(
        string question,
        string sql,
        object? results = null,
        int count = 5,
        CancellationToken cancellationToken = default)
    {
        var prompt = new StringBuilder();

        prompt.AppendLine("Generate relevant follow-up questions based on the original question and SQL query.");
        prompt.AppendLine();
        prompt.AppendLine("## Original Question");
        prompt.AppendLine(question);
        prompt.AppendLine();
        prompt.AppendLine("## SQL Query");
        prompt.AppendLine("```sql");
        prompt.AppendLine(sql);
        prompt.AppendLine("```");
        prompt.AppendLine();

        if (results != null)
        {
            prompt.AppendLine("## Query Results");
            prompt.AppendLine(results.ToString());
            prompt.AppendLine();
        }

        prompt.AppendLine("## Instructions");
        prompt.AppendLine($"Generate {count} relevant follow-up questions that a user might ask after seeing these results.");
        prompt.AppendLine("Focus on:");
        prompt.AppendLine("1. Drilling down into specific aspects of the data");
        prompt.AppendLine("2. Comparing with different time periods or categories");
        prompt.AppendLine("3. Exploring related metrics or dimensions");
        prompt.AppendLine("4. Understanding trends or patterns");
        prompt.AppendLine("5. Investigating outliers or anomalies");
        prompt.AppendLine();
        prompt.AppendLine("Return the questions as a numbered list, one per line.");

        return Task.FromResult(prompt.ToString());
    }

    public Task<string> BuildQuestionFromSqlPromptAsync(
        string sql,
        CancellationToken cancellationToken = default)
    {
        var prompt = new StringBuilder();

        prompt.AppendLine("Generate a natural language question that this SQL query answers.");
        prompt.AppendLine();
        prompt.AppendLine("## SQL Query");
        prompt.AppendLine("```sql");
        prompt.AppendLine(sql);
        prompt.AppendLine("```");
        prompt.AppendLine();
        prompt.AppendLine("## Instructions");
        prompt.AppendLine("1. Analyze the SQL query to understand what it does");
        prompt.AppendLine("2. Generate a clear, natural language question that this query would answer");
        prompt.AppendLine("3. Make the question specific and actionable");
        prompt.AppendLine("4. Use business-friendly language, not technical SQL terms");
        prompt.AppendLine("5. Return only the question, no additional explanation");

        return Task.FromResult(prompt.ToString());
    }

    public string BuildSystemPrompt(DatabaseConnection databaseConnection)
    {
        var prompt = new StringBuilder();

        prompt.AppendLine("You are an expert SQL assistant specialized in generating accurate SQL queries.");
        prompt.AppendLine();
        prompt.AppendLine("## Your Role");
        prompt.AppendLine("- Convert natural language questions into precise SQL queries");
        prompt.AppendLine("- Ensure queries are syntactically correct and optimized");
        prompt.AppendLine("- Follow database-specific SQL syntax and best practices");
        prompt.AppendLine("- Generate only SELECT statements for data retrieval");
        prompt.AppendLine();

        prompt.AppendLine("## Database Context");
        prompt.AppendLine($"You are working with a {databaseConnection.Type} database.");
        
        switch (databaseConnection.Type)
        {
            case Domain.Enums.DatabaseType.PostgreSql:
                prompt.AppendLine("- Use PostgreSQL syntax (double quotes for identifiers, LIMIT for row limiting)");
                prompt.AppendLine("- Support for advanced features like window functions, CTEs, and JSON operations");
                break;
            case Domain.Enums.DatabaseType.SqlServer:
                prompt.AppendLine("- Use SQL Server syntax (square brackets for identifiers, TOP for row limiting)");
                prompt.AppendLine("- Support for T-SQL features and functions");
                break;
            case Domain.Enums.DatabaseType.MySql:
                prompt.AppendLine("- Use MySQL syntax (backticks for identifiers, LIMIT for row limiting)");
                prompt.AppendLine("- Be aware of MySQL-specific functions and limitations");
                break;
            case Domain.Enums.DatabaseType.Sqlite:
                prompt.AppendLine("- Use SQLite syntax (simple and standard SQL)");
                prompt.AppendLine("- Limited function support compared to other databases");
                break;
        }

        prompt.AppendLine();
        prompt.AppendLine("## Security Requirements");
        prompt.AppendLine("- Generate only SELECT queries (no INSERT, UPDATE, DELETE, or DDL)");
        prompt.AppendLine("- Do not include any potentially harmful SQL patterns");
        prompt.AppendLine("- Use parameterized query patterns when applicable");
        prompt.AppendLine();

        prompt.AppendLine("## Quality Standards");
        prompt.AppendLine("- Write clear, readable SQL with proper formatting");
        prompt.AppendLine("- Use meaningful table aliases");
        prompt.AppendLine("- Include appropriate comments for complex logic");
        prompt.AppendLine("- Optimize for performance when possible");

        return prompt.ToString();
    }

    public string FormatTrainingDataForPrompt(IEnumerable<SimilarityResult> similarData)
    {
        if (!similarData.Any())
            return string.Empty;

        var formatted = new StringBuilder();

        foreach (var item in similarData.Take(5)) // Limit to top 5 most similar
        {
            formatted.AppendLine($"### Example (Similarity: {item.Score:P1})");
            
            // Check if this is a question-SQL pair
            if (item.Content.Contains("Q:") && item.Content.Contains("SQL:"))
            {
                var parts = item.Content.Split(new[] { "\nSQL:" }, System.StringSplitOptions.None);
                if (parts.Length == 2)
                {
                    var question = parts[0].Replace("Q:", "").Trim();
                    var sql = parts[1].Trim();
                    
                    formatted.AppendLine($"**Question:** {question}");
                    formatted.AppendLine("**SQL:**");
                    formatted.AppendLine("```sql");
                    formatted.AppendLine(sql);
                    formatted.AppendLine("```");
                }
                else
                {
                    formatted.AppendLine(item.Content);
                }
            }
            else
            {
                // DDL or documentation
                if (item.Metadata.TryGetValue("type", out var type))
                {
                    formatted.AppendLine($"**Type:** {type}");
                }
                
                formatted.AppendLine("**Content:**");
                if (item.Content.ToUpperInvariant().Contains("CREATE") || 
                    item.Content.ToUpperInvariant().Contains("ALTER"))
                {
                    formatted.AppendLine("```sql");
                    formatted.AppendLine(item.Content);
                    formatted.AppendLine("```");
                }
                else
                {
                    formatted.AppendLine(item.Content);
                }
            }
            
            formatted.AppendLine();
        }

        return formatted.ToString();
    }
}
