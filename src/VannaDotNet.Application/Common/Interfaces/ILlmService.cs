using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace VannaDotNet.Application.Common.Interfaces;

/// <summary>
/// Interface for Large Language Model services
/// </summary>
public interface ILlmService
{
    /// <summary>
    /// Generates a completion for the given prompt
    /// </summary>
    Task<string> GenerateCompletionAsync(
        string prompt,
        LlmOptions? options = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a completion for the given messages
    /// </summary>
    Task<string> GenerateCompletionAsync(
        IEnumerable<LlmMessage> messages,
        LlmOptions? options = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates an explanation for the given SQL query
    /// </summary>
    Task<string> GenerateExplanationAsync(
        string question,
        string sql,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates follow-up questions based on the query and results
    /// </summary>
    Task<IEnumerable<string>> GenerateFollowUpQuestionsAsync(
        string question,
        string sql,
        object? results = null,
        int count = 5,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a question from a SQL query
    /// </summary>
    Task<string> GenerateQuestionFromSqlAsync(
        string sql,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates if the service is available and configured correctly
    /// </summary>
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// LLM message for chat-based completions
/// </summary>
public record LlmMessage(LlmMessageRole Role, string Content);

/// <summary>
/// LLM message roles
/// </summary>
public enum LlmMessageRole
{
    System,
    User,
    Assistant
}

/// <summary>
/// Options for LLM generation
/// </summary>
public class LlmOptions
{
    /// <summary>
    /// Model to use for generation
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    /// Temperature for generation (0.0 to 1.0)
    /// </summary>
    public double Temperature { get; set; } = 0.1;

    /// <summary>
    /// Maximum number of tokens to generate
    /// </summary>
    public int MaxTokens { get; set; } = 4000;

    /// <summary>
    /// Stop sequences for generation
    /// </summary>
    public IEnumerable<string>? StopSequences { get; set; }

    /// <summary>
    /// Top-p sampling parameter
    /// </summary>
    public double? TopP { get; set; }

    /// <summary>
    /// Frequency penalty parameter
    /// </summary>
    public double? FrequencyPenalty { get; set; }

    /// <summary>
    /// Presence penalty parameter
    /// </summary>
    public double? PresencePenalty { get; set; }
}
