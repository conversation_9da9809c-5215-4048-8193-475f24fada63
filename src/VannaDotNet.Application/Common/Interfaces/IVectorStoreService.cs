using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace VannaDotNet.Application.Common.Interfaces;

/// <summary>
/// Interface for vector store services
/// </summary>
public interface IVectorStoreService
{
    /// <summary>
    /// Stores an embedding in the vector store
    /// </summary>
    Task StoreEmbeddingAsync(
        Guid id,
        float[] embedding,
        string content,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches for similar embeddings
    /// </summary>
    Task<IEnumerable<SimilarityResult>> SearchSimilarAsync(
        float[] queryEmbedding,
        int limit = 5,
        double minScore = 0.0,
        Dictionary<string, object>? filter = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches for similar embeddings using text query
    /// </summary>
    Task<IEnumerable<SimilarityResult>> SearchSimilarAsync(
        string query,
        int limit = 5,
        double minScore = 0.0,
        Dictionary<string, object>? filter = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing embedding
    /// </summary>
    Task UpdateEmbeddingAsync(
        Guid id,
        float[] embedding,
        string content,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes an embedding from the vector store
    /// </summary>
    Task DeleteEmbeddingAsync(
        Guid id,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets an embedding by ID
    /// </summary>
    Task<VectorStoreItem?> GetEmbeddingAsync(
        Guid id,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an embedding exists
    /// </summary>
    Task<bool> ExistsAsync(
        Guid id,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the count of embeddings in the store
    /// </summary>
    Task<long> GetCountAsync(
        Dictionary<string, object>? filter = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates or updates the collection/index
    /// </summary>
    Task InitializeAsync(
        int vectorSize = 1536,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes the collection/index
    /// </summary>
    Task DeleteCollectionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates if the service is available and configured correctly
    /// </summary>
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vector store statistics
    /// </summary>
    Task<VectorStoreStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Result of a similarity search
/// </summary>
public class SimilarityResult
{
    /// <summary>
    /// ID of the similar item
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Content of the similar item
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Similarity score (0.0 to 1.0)
    /// </summary>
    public double Score { get; set; }

    /// <summary>
    /// Metadata associated with the item
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Distance from the query vector
    /// </summary>
    public double Distance { get; set; }
}

/// <summary>
/// Vector store item
/// </summary>
public class VectorStoreItem
{
    /// <summary>
    /// ID of the item
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Vector embedding
    /// </summary>
    public float[] Embedding { get; set; } = Array.Empty<float>();

    /// <summary>
    /// Content of the item
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Metadata associated with the item
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Timestamp when the item was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Timestamp when the item was last updated
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Vector store statistics
/// </summary>
public class VectorStoreStatistics
{
    /// <summary>
    /// Total number of vectors in the store
    /// </summary>
    public long TotalVectors { get; set; }

    /// <summary>
    /// Size of each vector
    /// </summary>
    public int VectorSize { get; set; }

    /// <summary>
    /// Total storage size in bytes
    /// </summary>
    public long StorageSize { get; set; }

    /// <summary>
    /// Index status
    /// </summary>
    public string IndexStatus { get; set; } = string.Empty;

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime? LastUpdated { get; set; }

    /// <summary>
    /// Performance metrics
    /// </summary>
    public Dictionary<string, object> Metrics { get; set; } = new();
}
