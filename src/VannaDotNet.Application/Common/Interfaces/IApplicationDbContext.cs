using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using VannaDotNet.Domain.Entities;

namespace VannaDotNet.Application.Common.Interfaces;

/// <summary>
/// Application database context interface
/// </summary>
public interface IApplicationDbContext
{
    /// <summary>
    /// Training data entities
    /// </summary>
    DbSet<TrainingData> TrainingData { get; }

    /// <summary>
    /// Query session entities
    /// </summary>
    DbSet<QuerySession> QuerySessions { get; }

    /// <summary>
    /// Database connection entities
    /// </summary>
    DbSet<DatabaseConnection> DatabaseConnections { get; }

    /// <summary>
    /// Saves changes to the database
    /// </summary>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
