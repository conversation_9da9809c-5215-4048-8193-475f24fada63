using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace VannaDotNet.Application.Common.Interfaces;

/// <summary>
/// Interface for embedding generation services
/// </summary>
public interface IEmbeddingService
{
    /// <summary>
    /// Generates an embedding for the given text
    /// </summary>
    Task<float[]> GenerateEmbeddingAsync(
        string text,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates embeddings for multiple texts
    /// </summary>
    Task<IEnumerable<float[]>> GenerateEmbeddingsAsync(
        IEnumerable<string> texts,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the dimension of embeddings produced by this service
    /// </summary>
    int EmbeddingDimension { get; }

    /// <summary>
    /// Gets the maximum text length supported
    /// </summary>
    int MaxTextLength { get; }

    /// <summary>
    /// Validates if the service is available and configured correctly
    /// </summary>
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculates cosine similarity between two embeddings
    /// </summary>
    double CalculateCosineSimilarity(float[] embedding1, float[] embedding2);

    /// <summary>
    /// Calculates Euclidean distance between two embeddings
    /// </summary>
    double CalculateEuclideanDistance(float[] embedding1, float[] embedding2);

    /// <summary>
    /// Normalizes an embedding vector
    /// </summary>
    float[] NormalizeEmbedding(float[] embedding);
}
