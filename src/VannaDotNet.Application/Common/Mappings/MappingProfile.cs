using AutoMapper;
using VannaDotNet.Application.Common.Models;
using VannaDotNet.Domain.Entities;

namespace VannaDotNet.Application.Common.Mappings;

/// <summary>
/// AutoMapper profile for mapping between domain entities and DTOs
/// </summary>
public class MappingProfile : Profile
{
    public MappingProfile()
    {
        CreateMap<TrainingData, TrainingDataDto>()
            .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => src.Tags.ToList()))
            .ForMember(dest => dest.SqlQuery, opt => opt.MapFrom(src => src.SqlQuery))
            .ForMember(dest => dest.Question, opt => opt.MapFrom(src => src.Question));

        CreateMap<QuerySession, QuerySessionDto>()
            .ForMember(dest => dest.GeneratedSql, opt => opt.MapFrom(src => src.GeneratedSql != null ? src.GeneratedSql.Query : null))
            .ForMember(dest => dest.FollowUpQuestions, opt => opt.MapFrom(src => src.GetFollowUpQuestions()));

        CreateMap<DatabaseConnection, DatabaseConnectionDto>()
            .ForMember(dest => dest.AllowedSchemas, opt => opt.MapFrom(src => src.AllowedSchemas.ToList()));

        // Reverse mappings for creation scenarios
        CreateMap<CreateDdlTrainingDataRequest, TrainingData>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore());
    }
}

/// <summary>
/// DTO for database connection
/// </summary>
public class DatabaseConnectionDto
{
    public System.Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public Domain.Enums.DatabaseType Type { get; set; }
    public string? Description { get; set; }
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; }
    public string DatabaseName { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public bool IsReadOnly { get; set; }
    public bool IsActive { get; set; }
    public int MaxConnections { get; set; }
    public int TimeoutSeconds { get; set; }
    public int QueryTimeoutSeconds { get; set; }
    public int MaxRowLimit { get; set; }
    public System.Collections.Generic.List<string> AllowedSchemas { get; set; } = new();
    public System.DateTime? LastTestedAt { get; set; }
    public bool? LastTestResult { get; set; }
    public string? LastTestError { get; set; }
    public long SuccessfulQueries { get; set; }
    public long FailedQueries { get; set; }
    public System.TimeSpan TotalExecutionTime { get; set; }
    public string Environment { get; set; } = string.Empty;
    public System.DateTime CreatedAt { get; set; }
    public System.DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}
