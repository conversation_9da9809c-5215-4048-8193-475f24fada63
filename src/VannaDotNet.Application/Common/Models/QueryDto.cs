using System;
using System.Collections.Generic;
using VannaDotNet.Domain.Enums;

namespace VannaDotNet.Application.Common.Models;

/// <summary>
/// Data transfer object for query sessions
/// </summary>
public class QuerySessionDto
{
    /// <summary>
    /// Unique identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Natural language question
    /// </summary>
    public string Question { get; set; } = string.Empty;

    /// <summary>
    /// Generated SQL query
    /// </summary>
    public string? GeneratedSql { get; set; }

    /// <summary>
    /// Current status
    /// </summary>
    public QueryStatus Status { get; set; }

    /// <summary>
    /// Query results (JSON)
    /// </summary>
    public string? Results { get; set; }

    /// <summary>
    /// Confidence score
    /// </summary>
    public double? Confidence { get; set; }

    /// <summary>
    /// Execution time
    /// </summary>
    public TimeSpan? ExecutionTime { get; set; }

    /// <summary>
    /// Database connection ID
    /// </summary>
    public Guid DatabaseConnectionId { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Number of rows returned
    /// </summary>
    public int? RowCount { get; set; }

    /// <summary>
    /// Explanation of the SQL
    /// </summary>
    public string? Explanation { get; set; }

    /// <summary>
    /// Follow-up questions
    /// </summary>
    public List<string> FollowUpQuestions { get; set; } = new();

    /// <summary>
    /// User rating
    /// </summary>
    public int? UserRating { get; set; }

    /// <summary>
    /// User comments
    /// </summary>
    public string? UserComments { get; set; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// User who created the session
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;
}

/// <summary>
/// Request to generate SQL from a question
/// </summary>
public class GenerateSqlRequest
{
    /// <summary>
    /// Natural language question
    /// </summary>
    public string Question { get; set; } = string.Empty;

    /// <summary>
    /// Database connection ID
    /// </summary>
    public Guid DatabaseConnectionId { get; set; }

    /// <summary>
    /// Generation options
    /// </summary>
    public GenerationOptions? Options { get; set; }
}

/// <summary>
/// Request to execute a SQL query
/// </summary>
public class ExecuteSqlRequest
{
    /// <summary>
    /// SQL query to execute
    /// </summary>
    public string Sql { get; set; } = string.Empty;

    /// <summary>
    /// Database connection ID
    /// </summary>
    public Guid DatabaseConnectionId { get; set; }

    /// <summary>
    /// Execution options
    /// </summary>
    public ExecutionOptions? Options { get; set; }
}

/// <summary>
/// Request to ask a question (generate and optionally execute)
/// </summary>
public class AskQuestionRequest
{
    /// <summary>
    /// Natural language question
    /// </summary>
    public string Question { get; set; } = string.Empty;

    /// <summary>
    /// Database connection ID
    /// </summary>
    public Guid DatabaseConnectionId { get; set; }

    /// <summary>
    /// Whether to execute the generated SQL
    /// </summary>
    public bool Execute { get; set; } = false;

    /// <summary>
    /// Whether to generate visualizations
    /// </summary>
    public bool Visualize { get; set; } = false;

    /// <summary>
    /// Whether to automatically train on successful queries
    /// </summary>
    public bool AutoTrain { get; set; } = false;

    /// <summary>
    /// Generation options
    /// </summary>
    public GenerationOptions? GenerationOptions { get; set; }

    /// <summary>
    /// Execution options
    /// </summary>
    public ExecutionOptions? ExecutionOptions { get; set; }
}

/// <summary>
/// Options for SQL generation
/// </summary>
public class GenerationOptions
{
    /// <summary>
    /// Allow LLM to see data for introspection
    /// </summary>
    public bool AllowDataIntrospection { get; set; } = false;

    /// <summary>
    /// Maximum tokens for generation
    /// </summary>
    public int MaxTokens { get; set; } = 4000;

    /// <summary>
    /// Temperature for generation
    /// </summary>
    public double Temperature { get; set; } = 0.1;

    /// <summary>
    /// Model to use for generation
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    /// Include explanation in response
    /// </summary>
    public bool IncludeExplanation { get; set; } = true;

    /// <summary>
    /// Generate follow-up questions
    /// </summary>
    public bool GenerateFollowUpQuestions { get; set; } = true;

    /// <summary>
    /// Number of follow-up questions to generate
    /// </summary>
    public int FollowUpQuestionCount { get; set; } = 5;
}

/// <summary>
/// Options for SQL execution
/// </summary>
public class ExecutionOptions
{
    /// <summary>
    /// Maximum number of rows to return
    /// </summary>
    public int MaxRows { get; set; } = 1000;

    /// <summary>
    /// Query timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 60;

    /// <summary>
    /// Whether to include column metadata
    /// </summary>
    public bool IncludeMetadata { get; set; } = true;

    /// <summary>
    /// Format for results
    /// </summary>
    public ResultFormat Format { get; set; } = ResultFormat.Json;
}

/// <summary>
/// Result format options
/// </summary>
public enum ResultFormat
{
    Json,
    Csv,
    Table
}

/// <summary>
/// Response for SQL generation
/// </summary>
public class GenerateSqlResponse
{
    /// <summary>
    /// Generated SQL query
    /// </summary>
    public string Sql { get; set; } = string.Empty;

    /// <summary>
    /// Confidence score
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// Explanation of the SQL
    /// </summary>
    public string? Explanation { get; set; }

    /// <summary>
    /// Follow-up questions
    /// </summary>
    public List<string> FollowUpQuestions { get; set; } = new();

    /// <summary>
    /// Generation time
    /// </summary>
    public TimeSpan GenerationTime { get; set; }

    /// <summary>
    /// Session ID for tracking
    /// </summary>
    public Guid SessionId { get; set; }
}

/// <summary>
/// Response for SQL execution
/// </summary>
public class ExecuteSqlResponse
{
    /// <summary>
    /// Query results
    /// </summary>
    public object? Results { get; set; }

    /// <summary>
    /// Column metadata
    /// </summary>
    public List<ColumnMetadata> Columns { get; set; } = new();

    /// <summary>
    /// Number of rows returned
    /// </summary>
    public int RowCount { get; set; }

    /// <summary>
    /// Execution time
    /// </summary>
    public TimeSpan ExecutionTime { get; set; }

    /// <summary>
    /// Session ID for tracking
    /// </summary>
    public Guid SessionId { get; set; }
}

/// <summary>
/// Response for ask question request
/// </summary>
public class AskQuestionResponse
{
    /// <summary>
    /// Original question
    /// </summary>
    public string Question { get; set; } = string.Empty;

    /// <summary>
    /// Generated SQL
    /// </summary>
    public string Sql { get; set; } = string.Empty;

    /// <summary>
    /// Query results (if executed)
    /// </summary>
    public object? Results { get; set; }

    /// <summary>
    /// Column metadata (if executed)
    /// </summary>
    public List<ColumnMetadata> Columns { get; set; } = new();

    /// <summary>
    /// Number of rows returned
    /// </summary>
    public int? RowCount { get; set; }

    /// <summary>
    /// Confidence score
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// Explanation of the SQL
    /// </summary>
    public string? Explanation { get; set; }

    /// <summary>
    /// Follow-up questions
    /// </summary>
    public List<string> FollowUpQuestions { get; set; } = new();

    /// <summary>
    /// Visualization configuration (if requested)
    /// </summary>
    public object? Visualization { get; set; }

    /// <summary>
    /// Total processing time
    /// </summary>
    public TimeSpan TotalTime { get; set; }

    /// <summary>
    /// Session ID for tracking
    /// </summary>
    public Guid SessionId { get; set; }
}

/// <summary>
/// Column metadata
/// </summary>
public class ColumnMetadata
{
    /// <summary>
    /// Column name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Data type
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Whether the column is nullable
    /// </summary>
    public bool Nullable { get; set; }

    /// <summary>
    /// Whether the column is a primary key
    /// </summary>
    public bool PrimaryKey { get; set; }

    /// <summary>
    /// Maximum length (for string types)
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Precision (for numeric types)
    /// </summary>
    public int? Precision { get; set; }

    /// <summary>
    /// Scale (for decimal types)
    /// </summary>
    public int? Scale { get; set; }
}
