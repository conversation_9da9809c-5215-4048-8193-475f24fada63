using System;
using System.Collections.Generic;
using VannaDotNet.Domain.Enums;

namespace VannaDotNet.Application.Common.Models;

/// <summary>
/// Data transfer object for training data
/// </summary>
public class TrainingDataDto
{
    /// <summary>
    /// Unique identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Content of the training data
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Type of training data
    /// </summary>
    public TrainingDataType Type { get; set; }

    /// <summary>
    /// Description of the training data
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Tags associated with the training data
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Question (for QuestionSql type)
    /// </summary>
    public string? Question { get; set; }

    /// <summary>
    /// SQL query (for QuestionSql type)
    /// </summary>
    public string? SqlQuery { get; set; }

    /// <summary>
    /// Schema name
    /// </summary>
    public string? SchemaName { get; set; }

    /// <summary>
    /// Table name
    /// </summary>
    public string? TableName { get; set; }

    /// <summary>
    /// Indicates if the training data is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Quality score (0.0 to 1.0)
    /// </summary>
    public double QualityScore { get; set; }

    /// <summary>
    /// Number of times used
    /// </summary>
    public int UsageCount { get; set; }

    /// <summary>
    /// Last time used
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last update timestamp
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// User who created the training data
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// User who last updated the training data
    /// </summary>
    public string? UpdatedBy { get; set; }
}

/// <summary>
/// Request to create DDL training data
/// </summary>
public class CreateDdlTrainingDataRequest
{
    /// <summary>
    /// DDL statement
    /// </summary>
    public string Ddl { get; set; } = string.Empty;

    /// <summary>
    /// Description of the DDL
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Tags to associate with the training data
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Schema name
    /// </summary>
    public string? SchemaName { get; set; }

    /// <summary>
    /// Table name
    /// </summary>
    public string? TableName { get; set; }
}

/// <summary>
/// Request to create documentation training data
/// </summary>
public class CreateDocumentationTrainingDataRequest
{
    /// <summary>
    /// Documentation content
    /// </summary>
    public string Documentation { get; set; } = string.Empty;

    /// <summary>
    /// Description of the documentation
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Tags to associate with the training data
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Category of the documentation
    /// </summary>
    public string? Category { get; set; }
}

/// <summary>
/// Request to create question-SQL training data
/// </summary>
public class CreateQuestionSqlTrainingDataRequest
{
    /// <summary>
    /// Natural language question
    /// </summary>
    public string Question { get; set; } = string.Empty;

    /// <summary>
    /// SQL query that answers the question
    /// </summary>
    public string Sql { get; set; } = string.Empty;

    /// <summary>
    /// Description of the question-SQL pair
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Tags to associate with the training data
    /// </summary>
    public List<string>? Tags { get; set; }
}

/// <summary>
/// Request to update training data
/// </summary>
public class UpdateTrainingDataRequest
{
    /// <summary>
    /// ID of the training data to update
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// New content
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// New description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// New tags
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// New quality score
    /// </summary>
    public double? QualityScore { get; set; }

    /// <summary>
    /// Whether to activate or deactivate
    /// </summary>
    public bool? IsActive { get; set; }
}

/// <summary>
/// Response for training data operations
/// </summary>
public class TrainingDataResponse
{
    /// <summary>
    /// ID of the training data
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Type of training data
    /// </summary>
    public TrainingDataType Type { get; set; }

    /// <summary>
    /// Status of the operation
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Message describing the result
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Timestamp of the operation
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Paginated result for training data
/// </summary>
public class PagedTrainingDataResult
{
    /// <summary>
    /// Training data items
    /// </summary>
    public List<TrainingDataDto> Items { get; set; } = new();

    /// <summary>
    /// Current page number
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of items
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// Indicates if there is a next page
    /// </summary>
    public bool HasNextPage => Page < TotalPages;

    /// <summary>
    /// Indicates if there is a previous page
    /// </summary>
    public bool HasPreviousPage => Page > 1;
}
