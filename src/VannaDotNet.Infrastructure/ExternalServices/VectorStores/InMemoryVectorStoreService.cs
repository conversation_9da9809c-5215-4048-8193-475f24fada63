using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using VannaDotNet.Application.Common.Interfaces;

namespace VannaDotNet.Infrastructure.ExternalServices.VectorStores;

/// <summary>
/// In-memory implementation of IVectorStoreService for development and testing
/// </summary>
public class InMemoryVectorStoreService : IVectorStoreService
{
    private readonly ConcurrentDictionary<Guid, VectorStoreItem> _vectors = new();
    private readonly IEmbeddingService _embeddingService;
    private readonly ILogger<InMemoryVectorStoreService> _logger;
    private bool _isInitialized;

    public InMemoryVectorStoreService(IEmbeddingService embeddingService, ILogger<InMemoryVectorStoreService> logger)
    {
        _embeddingService = embeddingService;
        _logger = logger;
    }

    public async Task StoreEmbeddingAsync(
        Guid id,
        float[] embedding,
        string content,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        if (embedding == null || embedding.Length == 0)
        {
            throw new ArgumentException("Embedding cannot be null or empty", nameof(embedding));
        }

        if (string.IsNullOrWhiteSpace(content))
        {
            throw new ArgumentException("Content cannot be null or empty", nameof(content));
        }

        var item = new VectorStoreItem
        {
            Id = id,
            Embedding = embedding,
            Content = content,
            Metadata = metadata ?? new Dictionary<string, object>(),
            CreatedAt = DateTime.UtcNow
        };

        _vectors.AddOrUpdate(id, item, (key, existing) =>
        {
            existing.Embedding = embedding;
            existing.Content = content;
            existing.Metadata = metadata ?? new Dictionary<string, object>();
            existing.UpdatedAt = DateTime.UtcNow;
            return existing;
        });

        _logger.LogDebug("Stored embedding for ID {Id} with {Dimensions} dimensions", id, embedding.Length);

        await Task.CompletedTask;
    }

    public async Task<IEnumerable<SimilarityResult>> SearchSimilarAsync(
        float[] queryEmbedding,
        int limit = 5,
        double minScore = 0.0,
        Dictionary<string, object>? filter = null,
        CancellationToken cancellationToken = default)
    {
        if (queryEmbedding == null || queryEmbedding.Length == 0)
        {
            throw new ArgumentException("Query embedding cannot be null or empty", nameof(queryEmbedding));
        }

        var results = new List<SimilarityResult>();

        foreach (var kvp in _vectors)
        {
            var item = kvp.Value;

            // Apply filters if specified
            if (filter != null && !MatchesFilter(item, filter))
            {
                continue;
            }

            // Calculate cosine similarity
            var similarity = _embeddingService.CalculateCosineSimilarity(queryEmbedding, item.Embedding);

            if (similarity >= minScore)
            {
                results.Add(new SimilarityResult
                {
                    Id = item.Id,
                    Content = item.Content,
                    Score = similarity,
                    Distance = 1.0 - similarity,
                    Metadata = new Dictionary<string, object>(item.Metadata)
                });
            }
        }

        // Sort by similarity score (descending) and take top results
        var topResults = results
            .OrderByDescending(r => r.Score)
            .Take(limit)
            .ToList();

        _logger.LogDebug("Found {Count} similar vectors out of {Total} with min score {MinScore}", 
            topResults.Count, _vectors.Count, minScore);

        return await Task.FromResult(topResults);
    }

    public async Task<IEnumerable<SimilarityResult>> SearchSimilarAsync(
        string query,
        int limit = 5,
        double minScore = 0.0,
        Dictionary<string, object>? filter = null,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            throw new ArgumentException("Query cannot be null or empty", nameof(query));
        }

        // Generate embedding for the query
        var queryEmbedding = await _embeddingService.GenerateEmbeddingAsync(query, cancellationToken);

        return await SearchSimilarAsync(queryEmbedding, limit, minScore, filter, cancellationToken);
    }

    public async Task UpdateEmbeddingAsync(
        Guid id,
        float[] embedding,
        string content,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        await StoreEmbeddingAsync(id, embedding, content, metadata, cancellationToken);
    }

    public async Task DeleteEmbeddingAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var removed = _vectors.TryRemove(id, out var item);
        
        if (removed)
        {
            _logger.LogDebug("Deleted embedding for ID {Id}", id);
        }
        else
        {
            _logger.LogWarning("Attempted to delete non-existent embedding with ID {Id}", id);
        }

        await Task.CompletedTask;
    }

    public async Task<VectorStoreItem?> GetEmbeddingAsync(Guid id, CancellationToken cancellationToken = default)
    {
        _vectors.TryGetValue(id, out var item);
        return await Task.FromResult(item);
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(_vectors.ContainsKey(id));
    }

    public async Task<long> GetCountAsync(Dictionary<string, object>? filter = null, CancellationToken cancellationToken = default)
    {
        if (filter == null)
        {
            return await Task.FromResult(_vectors.Count);
        }

        var count = _vectors.Values.Count(item => MatchesFilter(item, filter));
        return await Task.FromResult(count);
    }

    public async Task InitializeAsync(int vectorSize = 1536, CancellationToken cancellationToken = default)
    {
        _isInitialized = true;
        _logger.LogInformation("Initialized in-memory vector store with vector size {VectorSize}", vectorSize);
        await Task.CompletedTask;
    }

    public async Task DeleteCollectionAsync(CancellationToken cancellationToken = default)
    {
        _vectors.Clear();
        _isInitialized = false;
        _logger.LogInformation("Cleared in-memory vector store");
        await Task.CompletedTask;
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(true); // In-memory store is always available
    }

    public async Task<VectorStoreStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var totalVectors = _vectors.Count;
        var vectorSize = totalVectors > 0 ? _vectors.Values.First().Embedding.Length : 0;
        var storageSize = totalVectors * vectorSize * sizeof(float); // Approximate

        var oldestItem = _vectors.Values.OrderBy(v => v.CreatedAt).FirstOrDefault();
        var newestItem = _vectors.Values.OrderByDescending(v => v.CreatedAt).FirstOrDefault();

        return await Task.FromResult(new VectorStoreStatistics
        {
            TotalVectors = totalVectors,
            VectorSize = vectorSize,
            StorageSize = storageSize,
            IndexStatus = _isInitialized ? "Ready" : "Not Initialized",
            LastUpdated = newestItem?.UpdatedAt ?? newestItem?.CreatedAt,
            Metrics = new Dictionary<string, object>
            {
                ["oldest_vector"] = oldestItem?.CreatedAt.ToString() ?? "N/A",
                ["newest_vector"] = newestItem?.CreatedAt.ToString() ?? "N/A",
                ["memory_usage_bytes"] = storageSize
            }
        });
    }

    /// <summary>
    /// Checks if a vector store item matches the given filter
    /// </summary>
    private static bool MatchesFilter(VectorStoreItem item, Dictionary<string, object> filter)
    {
        foreach (var filterKvp in filter)
        {
            if (!item.Metadata.TryGetValue(filterKvp.Key, out var value))
            {
                return false; // Metadata key not found
            }

            // Simple equality check - in a real implementation, you might want more sophisticated filtering
            if (!value.Equals(filterKvp.Value))
            {
                return false;
            }
        }

        return true;
    }
}
