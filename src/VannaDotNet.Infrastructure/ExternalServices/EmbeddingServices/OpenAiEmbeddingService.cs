using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Azure;
using Azure.AI.OpenAI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using VannaDotNet.Application.Common.Interfaces;

namespace VannaDotNet.Infrastructure.ExternalServices.EmbeddingServices;

/// <summary>
/// OpenAI implementation of IEmbeddingService
/// </summary>
public class OpenAiEmbeddingService : IEmbeddingService
{
    private readonly OpenAIClient _client;
    private readonly OpenAiEmbeddingConfiguration _configuration;
    private readonly ILogger<OpenAiEmbeddingService> _logger;

    public OpenAiEmbeddingService(IOptions<OpenAiEmbeddingConfiguration> configuration, ILogger<OpenAiEmbeddingService> logger)
    {
        _configuration = configuration.Value;
        _logger = logger;

        // Initialize OpenAI client
        if (!string.IsNullOrEmpty(_configuration.AzureEndpoint))
        {
            // Azure OpenAI
            _client = new OpenAIClient(
                new Uri(_configuration.AzureEndpoint),
                new AzureKeyCredential(_configuration.ApiKey));
        }
        else
        {
            // OpenAI
            _client = new OpenAIClient(_configuration.ApiKey);
        }
    }

    public int EmbeddingDimension => _configuration.EmbeddingDimension;

    public int MaxTextLength => _configuration.MaxTextLength;

    public async Task<float[]> GenerateEmbeddingAsync(string text, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            throw new ArgumentException("Text cannot be null or empty", nameof(text));
        }

        if (text.Length > MaxTextLength)
        {
            _logger.LogWarning("Text length {Length} exceeds maximum {MaxLength}, truncating", 
                text.Length, MaxTextLength);
            text = text.Substring(0, MaxTextLength);
        }

        try
        {
            var options = new EmbeddingsOptions(_configuration.Model, new[] { text });

            _logger.LogDebug("Generating embedding for text of length {Length}", text.Length);

            var response = await _client.GetEmbeddingsAsync(options, cancellationToken);

            if (response?.Value?.Data?.Count > 0)
            {
                var embedding = response.Value.Data[0].Embedding.ToArray();
                
                _logger.LogDebug("Generated embedding with {Dimensions} dimensions", embedding.Length);

                return embedding;
            }

            _logger.LogWarning("No embedding data returned from OpenAI");
            throw new InvalidOperationException("No embedding data returned from OpenAI");
        }
        catch (RequestFailedException ex)
        {
            _logger.LogError(ex, "OpenAI API request failed with status {StatusCode}: {Message}", 
                ex.Status, ex.Message);
            throw new InvalidOperationException($"OpenAI API request failed: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embedding for text");
            throw new InvalidOperationException("Failed to generate embedding", ex);
        }
    }

    public async Task<IEnumerable<float[]>> GenerateEmbeddingsAsync(IEnumerable<string> texts, CancellationToken cancellationToken = default)
    {
        var textList = texts.ToList();
        
        if (!textList.Any())
        {
            return new List<float[]>();
        }

        // Validate and truncate texts if necessary
        var processedTexts = textList.Select(text =>
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                throw new ArgumentException("Text cannot be null or empty");
            }

            if (text.Length > MaxTextLength)
            {
                _logger.LogWarning("Text length {Length} exceeds maximum {MaxLength}, truncating", 
                    text.Length, MaxTextLength);
                return text.Substring(0, MaxTextLength);
            }

            return text;
        }).ToList();

        try
        {
            var options = new EmbeddingsOptions(_configuration.Model, processedTexts);

            _logger.LogDebug("Generating embeddings for {Count} texts", processedTexts.Count);

            var response = await _client.GetEmbeddingsAsync(options, cancellationToken);

            if (response?.Value?.Data?.Count > 0)
            {
                var embeddings = response.Value.Data
                    .Select(data => data.Embedding.ToArray())
                    .ToList();

                _logger.LogDebug("Generated {Count} embeddings with {Dimensions} dimensions each", 
                    embeddings.Count, embeddings.FirstOrDefault()?.Length ?? 0);

                return embeddings;
            }

            _logger.LogWarning("No embedding data returned from OpenAI");
            throw new InvalidOperationException("No embedding data returned from OpenAI");
        }
        catch (RequestFailedException ex)
        {
            _logger.LogError(ex, "OpenAI API request failed with status {StatusCode}: {Message}", 
                ex.Status, ex.Message);
            throw new InvalidOperationException($"OpenAI API request failed: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embeddings for texts");
            throw new InvalidOperationException("Failed to generate embeddings", ex);
        }
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var testOptions = new EmbeddingsOptions(_configuration.Model, new[] { "test" });
            var response = await _client.GetEmbeddingsAsync(testOptions, cancellationToken);
            return response?.Value != null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "OpenAI embedding service availability check failed");
            return false;
        }
    }

    public double CalculateCosineSimilarity(float[] embedding1, float[] embedding2)
    {
        if (embedding1 == null || embedding2 == null)
        {
            throw new ArgumentNullException("Embeddings cannot be null");
        }

        if (embedding1.Length != embedding2.Length)
        {
            throw new ArgumentException("Embeddings must have the same dimension");
        }

        var dotProduct = 0.0;
        var magnitude1 = 0.0;
        var magnitude2 = 0.0;

        for (int i = 0; i < embedding1.Length; i++)
        {
            dotProduct += embedding1[i] * embedding2[i];
            magnitude1 += embedding1[i] * embedding1[i];
            magnitude2 += embedding2[i] * embedding2[i];
        }

        magnitude1 = Math.Sqrt(magnitude1);
        magnitude2 = Math.Sqrt(magnitude2);

        if (magnitude1 == 0.0 || magnitude2 == 0.0)
        {
            return 0.0;
        }

        return dotProduct / (magnitude1 * magnitude2);
    }

    public double CalculateEuclideanDistance(float[] embedding1, float[] embedding2)
    {
        if (embedding1 == null || embedding2 == null)
        {
            throw new ArgumentNullException("Embeddings cannot be null");
        }

        if (embedding1.Length != embedding2.Length)
        {
            throw new ArgumentException("Embeddings must have the same dimension");
        }

        var sumSquaredDifferences = 0.0;

        for (int i = 0; i < embedding1.Length; i++)
        {
            var difference = embedding1[i] - embedding2[i];
            sumSquaredDifferences += difference * difference;
        }

        return Math.Sqrt(sumSquaredDifferences);
    }

    public float[] NormalizeEmbedding(float[] embedding)
    {
        if (embedding == null)
        {
            throw new ArgumentNullException(nameof(embedding));
        }

        var magnitude = Math.Sqrt(embedding.Sum(x => x * x));

        if (magnitude == 0.0)
        {
            return embedding;
        }

        return embedding.Select(x => (float)(x / magnitude)).ToArray();
    }
}

/// <summary>
/// Configuration for OpenAI embedding service
/// </summary>
public class OpenAiEmbeddingConfiguration
{
    public const string SectionName = "OpenAIEmbedding";

    /// <summary>
    /// OpenAI API key
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// Azure OpenAI endpoint (if using Azure OpenAI)
    /// </summary>
    public string? AzureEndpoint { get; set; }

    /// <summary>
    /// Embedding model to use
    /// </summary>
    public string Model { get; set; } = "text-embedding-ada-002";

    /// <summary>
    /// Dimension of embeddings produced by the model
    /// </summary>
    public int EmbeddingDimension { get; set; } = 1536;

    /// <summary>
    /// Maximum text length supported by the model
    /// </summary>
    public int MaxTextLength { get; set; } = 8191;

    /// <summary>
    /// Request timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 60;

    /// <summary>
    /// Maximum retry attempts
    /// </summary>
    public int MaxRetries { get; set; } = 3;
}
