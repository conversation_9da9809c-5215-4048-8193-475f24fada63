using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Azure;
using Azure.AI.OpenAI;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using VannaDotNet.Application.Common.Interfaces;

namespace VannaDotNet.Infrastructure.ExternalServices.LlmServices;

/// <summary>
/// OpenAI implementation of ILlmService
/// </summary>
public class OpenAiService : ILlmService
{
    private readonly OpenAIClient _client;
    private readonly OpenAiConfiguration _configuration;
    private readonly ILogger<OpenAiService> _logger;

    public OpenAiService(IOptions<OpenAiConfiguration> configuration, ILogger<OpenAiService> logger)
    {
        _configuration = configuration.Value;
        _logger = logger;

        // Initialize OpenAI client
        if (!string.IsNullOrEmpty(_configuration.AzureEndpoint))
        {
            // Azure OpenAI
            _client = new OpenAIClient(
                new Uri(_configuration.AzureEndpoint),
                new AzureKeyCredential(_configuration.ApiKey));
        }
        else
        {
            // OpenAI
            _client = new OpenAIClient(_configuration.ApiKey);
        }
    }

    public async Task<string> GenerateCompletionAsync(
        string prompt,
        LlmOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var messages = new List<LlmMessage>
            {
                new(LlmMessageRole.User, prompt)
            };

            return await GenerateCompletionAsync(messages, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating completion for prompt");
            throw new InvalidOperationException("Failed to generate completion", ex);
        }
    }

    public async Task<string> GenerateCompletionAsync(
        IEnumerable<LlmMessage> messages,
        LlmOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var chatOptions = new ChatCompletionsOptions
            {
                DeploymentName = options?.Model ?? _configuration.DefaultModel,
                Temperature = (float)(options?.Temperature ?? _configuration.DefaultTemperature),
                MaxTokens = options?.MaxTokens ?? _configuration.DefaultMaxTokens
            };

            // Add frequency and presence penalties if specified
            if (options?.FrequencyPenalty.HasValue == true)
            {
                chatOptions.FrequencyPenalty = (float)options.FrequencyPenalty.Value;
            }

            if (options?.PresencePenalty.HasValue == true)
            {
                chatOptions.PresencePenalty = (float)options.PresencePenalty.Value;
            }

            if (options?.TopP.HasValue == true)
            {
                chatOptions.NucleusSamplingFactor = (float)options.TopP.Value;
            }

            // Add stop sequences if specified
            if (options?.StopSequences != null)
            {
                foreach (var stopSequence in options.StopSequences)
                {
                    chatOptions.StopSequences.Add(stopSequence);
                }
            }

            // Convert messages to OpenAI format
            foreach (var message in messages)
            {
                ChatRequestMessage chatMessage = message.Role switch
                {
                    LlmMessageRole.System => new ChatRequestSystemMessage(message.Content),
                    LlmMessageRole.User => new ChatRequestUserMessage(message.Content),
                    LlmMessageRole.Assistant => new ChatRequestAssistantMessage(message.Content),
                    _ => throw new ArgumentException($"Unknown message role: {message.Role}")
                };

                chatOptions.Messages.Add(chatMessage);
            }

            _logger.LogDebug("Sending chat completion request to OpenAI with {MessageCount} messages", 
                chatOptions.Messages.Count);

            var response = await _client.GetChatCompletionsAsync(chatOptions, cancellationToken);

            if (response?.Value?.Choices?.Count > 0)
            {
                var content = response.Value.Choices[0].Message.Content;
                
                _logger.LogDebug("Received completion response with {TokenCount} tokens", 
                    response.Value.Usage?.TotalTokens ?? 0);

                return content ?? string.Empty;
            }

            _logger.LogWarning("No completion choices returned from OpenAI");
            return string.Empty;
        }
        catch (RequestFailedException ex)
        {
            _logger.LogError(ex, "OpenAI API request failed with status {StatusCode}: {Message}", 
                ex.Status, ex.Message);
            throw new InvalidOperationException($"OpenAI API request failed: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating completion with messages");
            throw new InvalidOperationException("Failed to generate completion", ex);
        }
    }

    public async Task<string> GenerateExplanationAsync(
        string question,
        string sql,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var prompt = $@"Explain the following SQL query in simple, non-technical terms:

Question: {question}

SQL Query:
```sql
{sql}
```

Provide a clear explanation of:
1. What data the query retrieves
2. How it processes the data
3. What the expected output format is

Keep the explanation accessible to non-technical users.";

            var options = new LlmOptions
            {
                Temperature = 0.3,
                MaxTokens = 500
            };

            return await GenerateCompletionAsync(prompt, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating explanation for SQL query");
            throw new InvalidOperationException("Failed to generate explanation", ex);
        }
    }

    public async Task<IEnumerable<string>> GenerateFollowUpQuestionsAsync(
        string question,
        string sql,
        object? results = null,
        int count = 5,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var prompt = $@"Based on the following question and SQL query, generate {count} relevant follow-up questions that a user might ask:

Original Question: {question}

SQL Query:
```sql
{sql}
```

Generate follow-up questions that:
1. Drill down into specific aspects of the data
2. Compare with different time periods or categories
3. Explore related metrics or dimensions
4. Investigate trends or patterns

Return only the questions, one per line, numbered 1-{count}.";

            var options = new LlmOptions
            {
                Temperature = 0.7,
                MaxTokens = 300
            };

            var response = await GenerateCompletionAsync(prompt, options, cancellationToken);

            // Parse the response to extract questions
            var questions = response
                .Split('\n', StringSplitOptions.RemoveEmptyEntries)
                .Where(line => !string.IsNullOrWhiteSpace(line))
                .Select(line => line.Trim())
                .Where(line => char.IsDigit(line[0])) // Lines starting with numbers
                .Select(line => line.Substring(line.IndexOf('.') + 1).Trim()) // Remove numbering
                .Take(count)
                .ToList();

            return questions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating follow-up questions");
            return new List<string>();
        }
    }

    public async Task<string> GenerateQuestionFromSqlAsync(
        string sql,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var prompt = $@"Generate a natural language question that this SQL query answers:

SQL Query:
```sql
{sql}
```

Requirements:
1. Make the question clear and specific
2. Use business-friendly language, not technical SQL terms
3. Focus on what business insight the query provides
4. Return only the question, no additional explanation

Question:";

            var options = new LlmOptions
            {
                Temperature = 0.3,
                MaxTokens = 100
            };

            return await GenerateCompletionAsync(prompt, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating question from SQL");
            throw new InvalidOperationException("Failed to generate question from SQL", ex);
        }
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var testOptions = new ChatCompletionsOptions
            {
                DeploymentName = _configuration.DefaultModel,
                MaxTokens = 1
            };

            testOptions.Messages.Add(new ChatRequestUserMessage("Test"));

            var response = await _client.GetChatCompletionsAsync(testOptions, cancellationToken);
            return response?.Value != null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "OpenAI service availability check failed");
            return false;
        }
    }
}

/// <summary>
/// Configuration for OpenAI service
/// </summary>
public class OpenAiConfiguration
{
    public const string SectionName = "OpenAI";

    /// <summary>
    /// OpenAI API key
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// Azure OpenAI endpoint (if using Azure OpenAI)
    /// </summary>
    public string? AzureEndpoint { get; set; }

    /// <summary>
    /// Default model to use
    /// </summary>
    public string DefaultModel { get; set; } = "gpt-4";

    /// <summary>
    /// Default temperature for generation
    /// </summary>
    public double DefaultTemperature { get; set; } = 0.1;

    /// <summary>
    /// Default maximum tokens
    /// </summary>
    public int DefaultMaxTokens { get; set; } = 4000;

    /// <summary>
    /// Request timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 60;

    /// <summary>
    /// Maximum retry attempts
    /// </summary>
    public int MaxRetries { get; set; } = 3;
}
