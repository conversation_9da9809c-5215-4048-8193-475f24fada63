using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Enums;
using VannaDotNet.Domain.Repositories;

namespace VannaDotNet.Infrastructure.Data.Repositories;

/// <summary>
/// Entity Framework implementation of IQuerySessionRepository
/// </summary>
public class QuerySessionRepository : IQuerySessionRepository
{
    private readonly ApplicationDbContext _context;

    public QuerySessionRepository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<QuerySession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<QuerySession>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<QuerySession>> GetByStatusAsync(QueryStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .Where(x => x.Status == status)
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<QuerySession>> GetByDatabaseConnectionAsync(Guid databaseConnectionId, CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .Where(x => x.DatabaseConnectionId == databaseConnectionId)
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<QuerySession>> GetByUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .Where(x => x.CreatedBy == userId)
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<QuerySession> Items, int TotalCount)> GetPaginatedAsync(
        int page,
        int pageSize,
        QueryStatus? status = null,
        Guid? databaseConnectionId = null,
        string? userId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.QuerySessions.AsQueryable();

        // Apply filters
        if (status.HasValue)
        {
            query = query.Where(x => x.Status == status.Value);
        }

        if (databaseConnectionId.HasValue)
        {
            query = query.Where(x => x.DatabaseConnectionId == databaseConnectionId.Value);
        }

        if (!string.IsNullOrEmpty(userId))
        {
            query = query.Where(x => x.CreatedBy == userId);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(x => x.CreatedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(x => x.CreatedAt <= toDate.Value);
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and ordering
        var items = await query
            .OrderByDescending(x => x.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<IEnumerable<QuerySession>> SearchByQuestionAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .Where(x => EF.Functions.ILike(x.Question, $"%{searchTerm}%"))
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<QuerySession>> GetSuccessfulForTrainingAsync(
        int limit,
        double minConfidence = 0.7,
        int? minRating = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.QuerySessions
            .Where(x => x.Status == QueryStatus.Executed && 
                       x.Confidence >= minConfidence &&
                       !x.UsedForTraining);

        if (minRating.HasValue)
        {
            query = query.Where(x => x.UserRating >= minRating.Value);
        }

        return await query
            .OrderByDescending(x => x.Confidence)
            .ThenByDescending(x => x.UserRating)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<QuerySession>> GetHighRatedAsync(int minRating, int limit, CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .Where(x => x.UserRating >= minRating)
            .OrderByDescending(x => x.UserRating)
            .ThenByDescending(x => x.Confidence)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<QuerySession>> GetRecentAsync(int limit, CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .OrderByDescending(x => x.CreatedAt)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<QuerySession>> GetSlowQueriesAsync(TimeSpan minExecutionTime, int limit, CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .Where(x => x.ExecutionTime >= minExecutionTime)
            .OrderByDescending(x => x.ExecutionTime)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<QuerySession>> GetFailedAsync(int limit, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.QuerySessions
            .Where(x => x.Status == QueryStatus.Failed);

        if (fromDate.HasValue)
        {
            query = query.Where(x => x.CreatedAt >= fromDate.Value);
        }

        return await query
            .OrderByDescending(x => x.CreatedAt)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<QuerySession>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .Where(x => x.CreatedAt >= fromDate && x.CreatedAt <= toDate)
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<QuerySession> AddAsync(QuerySession querySession, CancellationToken cancellationToken = default)
    {
        _context.QuerySessions.Add(querySession);
        await _context.SaveChangesAsync(cancellationToken);
        return querySession;
    }

    public async Task UpdateAsync(QuerySession querySession, CancellationToken cancellationToken = default)
    {
        _context.QuerySessions.Update(querySession);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            _context.QuerySessions.Remove(entity);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task SoftDeleteAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            entity.Delete(deletedBy);
            await UpdateAsync(entity, cancellationToken);
        }
    }

    public async Task<IEnumerable<QuerySession>> GetAvailableForTrainingAsync(
        int limit,
        double minConfidence = 0.8,
        int? minRating = 4,
        CancellationToken cancellationToken = default)
    {
        var query = _context.QuerySessions
            .Where(x => x.Status == QueryStatus.Executed && 
                       x.Confidence >= minConfidence &&
                       !x.UsedForTraining);

        if (minRating.HasValue)
        {
            query = query.Where(x => x.UserRating >= minRating.Value);
        }

        return await query
            .OrderByDescending(x => x.Confidence)
            .ThenByDescending(x => x.UserRating)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task MarkAsUsedForTrainingAsync(IEnumerable<Guid> sessionIds, CancellationToken cancellationToken = default)
    {
        var sessions = await _context.QuerySessions
            .Where(x => sessionIds.Contains(x.Id))
            .ToListAsync(cancellationToken);

        foreach (var session in sessions)
        {
            session.MarkAsUsedForTraining();
        }

        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<QuerySessionStatistics> GetStatisticsAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        Guid? databaseConnectionId = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.QuerySessions.AsQueryable();

        if (fromDate.HasValue)
        {
            query = query.Where(x => x.CreatedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(x => x.CreatedAt <= toDate.Value);
        }

        if (databaseConnectionId.HasValue)
        {
            query = query.Where(x => x.DatabaseConnectionId == databaseConnectionId.Value);
        }

        var totalSessions = await query.CountAsync(cancellationToken);
        var successfulSessions = await query.CountAsync(x => x.Status == QueryStatus.Executed, cancellationToken);
        var failedSessions = await query.CountAsync(x => x.Status == QueryStatus.Failed, cancellationToken);
        var cancelledSessions = await query.CountAsync(x => x.Status == QueryStatus.Cancelled, cancellationToken);

        var successRate = totalSessions > 0 ? (double)successfulSessions / totalSessions : 0.0;

        var avgExecutionTime = await query
            .Where(x => x.ExecutionTime.HasValue)
            .AverageAsync(x => x.ExecutionTime!.Value.TotalMilliseconds, cancellationToken);

        var avgGenerationTime = await query
            .Where(x => x.GenerationStartedAt.HasValue && x.GenerationCompletedAt.HasValue)
            .AverageAsync(x => (x.GenerationCompletedAt!.Value - x.GenerationStartedAt!.Value).TotalMilliseconds, cancellationToken);

        var avgConfidence = await query
            .Where(x => x.Confidence.HasValue)
            .AverageAsync(x => x.Confidence!.Value, cancellationToken);

        var avgUserRating = await query
            .Where(x => x.UserRating.HasValue)
            .AverageAsync(x => (double)x.UserRating!.Value, cancellationToken);

        var sessionsByStatus = await query
            .GroupBy(x => x.Status)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);

        var sessionsUsedForTraining = await query.CountAsync(x => x.UsedForTraining, cancellationToken);

        return new QuerySessionStatistics
        {
            TotalSessions = totalSessions,
            SuccessfulSessions = successfulSessions,
            FailedSessions = failedSessions,
            CancelledSessions = cancelledSessions,
            SuccessRate = successRate,
            AverageExecutionTime = TimeSpan.FromMilliseconds(avgExecutionTime),
            AverageGenerationTime = TimeSpan.FromMilliseconds(avgGenerationTime),
            AverageConfidence = avgConfidence,
            AverageUserRating = avgUserRating,
            SessionsByStatus = sessionsByStatus,
            SessionsUsedForTraining = sessionsUsedForTraining
        };
    }

    public async Task<QueryPerformanceMetrics> GetPerformanceMetricsAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.QuerySessions
            .Where(x => x.ExecutionTime.HasValue);

        if (fromDate.HasValue)
        {
            query = query.Where(x => x.CreatedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(x => x.CreatedAt <= toDate.Value);
        }

        var executionTimes = await query
            .Select(x => x.ExecutionTime!.Value.TotalMilliseconds)
            .OrderBy(x => x)
            .ToListAsync(cancellationToken);

        var totalRowsReturned = await query
            .Where(x => x.RowCount.HasValue)
            .SumAsync(x => x.RowCount!.Value, cancellationToken);

        var avgRowsPerQuery = await query
            .Where(x => x.RowCount.HasValue)
            .AverageAsync(x => (double)x.RowCount!.Value, cancellationToken);

        var queriesWithErrors = await _context.QuerySessions
            .CountAsync(x => x.Status == QueryStatus.Failed, cancellationToken);

        return new QueryPerformanceMetrics
        {
            FastestExecution = executionTimes.Any() ? TimeSpan.FromMilliseconds(executionTimes.First()) : TimeSpan.Zero,
            SlowestExecution = executionTimes.Any() ? TimeSpan.FromMilliseconds(executionTimes.Last()) : TimeSpan.Zero,
            MedianExecutionTime = executionTimes.Any() ? TimeSpan.FromMilliseconds(executionTimes[executionTimes.Count / 2]) : TimeSpan.Zero,
            P95ExecutionTime = executionTimes.Any() ? TimeSpan.FromMilliseconds(executionTimes[(int)(executionTimes.Count * 0.95)]) : TimeSpan.Zero,
            P99ExecutionTime = executionTimes.Any() ? TimeSpan.FromMilliseconds(executionTimes[(int)(executionTimes.Count * 0.99)]) : TimeSpan.Zero,
            TotalRowsReturned = totalRowsReturned,
            AverageRowsPerQuery = avgRowsPerQuery,
            QueriesWithErrors = queriesWithErrors,
            ErrorsByType = new Dictionary<string, int>() // TODO: Implement error categorization
        };
    }

    public async Task<IEnumerable<PopularQuestion>> GetPopularQuestionsAsync(int limit, CancellationToken cancellationToken = default)
    {
        return await _context.QuerySessions
            .Where(x => x.Status == QueryStatus.Executed)
            .GroupBy(x => x.Question.ToLower())
            .Select(g => new PopularQuestion
            {
                Question = g.First().Question,
                Count = g.Count(),
                AverageConfidence = g.Average(x => x.Confidence ?? 0),
                AverageRating = g.Average(x => x.UserRating ?? 0),
                LastAsked = g.Max(x => x.CreatedAt)
            })
            .OrderByDescending(x => x.Count)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task CleanupOldSessionsAsync(DateTime olderThan, CancellationToken cancellationToken = default)
    {
        var oldSessions = await _context.QuerySessions
            .Where(x => x.CreatedAt < olderThan && !x.UsedForTraining)
            .ToListAsync(cancellationToken);

        _context.QuerySessions.RemoveRange(oldSessions);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
