using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Enums;
using VannaDotNet.Domain.Repositories;

namespace VannaDotNet.Infrastructure.Data.Repositories;

/// <summary>
/// Entity Framework implementation of ITrainingDataRepository
/// </summary>
public class TrainingDataRepository : ITrainingDataRepository
{
    private readonly ApplicationDbContext _context;

    public TrainingDataRepository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<TrainingData?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<TrainingData>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<TrainingData>> GetByTypeAsync(TrainingDataType type, CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .Where(x => x.Type == type)
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<TrainingData>> GetActiveByTypeAsync(TrainingDataType type, CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .Where(x => x.Type == type && x.IsActive)
            .OrderByDescending(x => x.QualityScore)
            .ThenByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<TrainingData>> SearchByTagsAsync(IEnumerable<string> tags, CancellationToken cancellationToken = default)
    {
        var tagList = tags.Select(t => t.ToLowerInvariant()).ToList();
        
        return await _context.TrainingData
            .Where(x => x.Tags.Any(tag => tagList.Contains(tag.ToLower())))
            .OrderByDescending(x => x.QualityScore)
            .ThenByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<TrainingData>> SearchByContentAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .Where(x => EF.Functions.ILike(x.Content, $"%{searchTerm}%") ||
                       (x.Description != null && EF.Functions.ILike(x.Description, $"%{searchTerm}%")))
            .OrderByDescending(x => x.QualityScore)
            .ThenByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<TrainingData>> GetBySchemaAndTableAsync(string? schemaName, string? tableName, CancellationToken cancellationToken = default)
    {
        var query = _context.TrainingData.AsQueryable();

        if (!string.IsNullOrEmpty(schemaName))
        {
            query = query.Where(x => x.SchemaName == schemaName);
        }

        if (!string.IsNullOrEmpty(tableName))
        {
            query = query.Where(x => x.TableName == tableName);
        }

        return await query
            .OrderByDescending(x => x.QualityScore)
            .ThenByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<TrainingData> Items, int TotalCount)> GetPaginatedAsync(
        int page, 
        int pageSize, 
        TrainingDataType? type = null,
        bool? isActive = null,
        IEnumerable<string>? tags = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.TrainingData.AsQueryable();

        // Apply filters
        if (type.HasValue)
        {
            query = query.Where(x => x.Type == type.Value);
        }

        if (isActive.HasValue)
        {
            query = query.Where(x => x.IsActive == isActive.Value);
        }

        if (tags != null && tags.Any())
        {
            var tagList = tags.Select(t => t.ToLowerInvariant()).ToList();
            query = query.Where(x => x.Tags.Any(tag => tagList.Contains(tag.ToLower())));
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(x => EF.Functions.ILike(x.Content, $"%{searchTerm}%") ||
                                   (x.Description != null && EF.Functions.ILike(x.Description, $"%{searchTerm}%")));
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and ordering
        var items = await query
            .OrderByDescending(x => x.QualityScore)
            .ThenByDescending(x => x.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<IEnumerable<TrainingData>> GetHighQualityAsync(double minQualityScore, int limit, CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .Where(x => x.IsActive && x.QualityScore >= minQualityScore)
            .OrderByDescending(x => x.QualityScore)
            .ThenByDescending(x => x.UsageCount)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<TrainingData>> GetMostUsedAsync(int limit, CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .Where(x => x.IsActive)
            .OrderByDescending(x => x.UsageCount)
            .ThenByDescending(x => x.QualityScore)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<TrainingData>> GetRecentAsync(int limit, CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .Where(x => x.IsActive)
            .OrderByDescending(x => x.CreatedAt)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<TrainingData> AddAsync(TrainingData trainingData, CancellationToken cancellationToken = default)
    {
        _context.TrainingData.Add(trainingData);
        await _context.SaveChangesAsync(cancellationToken);
        return trainingData;
    }

    public async Task<IEnumerable<TrainingData>> AddRangeAsync(IEnumerable<TrainingData> trainingDataList, CancellationToken cancellationToken = default)
    {
        var list = trainingDataList.ToList();
        _context.TrainingData.AddRange(list);
        await _context.SaveChangesAsync(cancellationToken);
        return list;
    }

    public async Task UpdateAsync(TrainingData trainingData, CancellationToken cancellationToken = default)
    {
        _context.TrainingData.Update(trainingData);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateRangeAsync(IEnumerable<TrainingData> trainingDataList, CancellationToken cancellationToken = default)
    {
        _context.TrainingData.UpdateRange(trainingDataList);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            _context.TrainingData.Remove(entity);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task SoftDeleteAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            entity.Delete(deletedBy);
            await UpdateAsync(entity, cancellationToken);
        }
    }

    public async Task RestoreAsync(Guid id, string restoredBy, CancellationToken cancellationToken = default)
    {
        // Need to include deleted entities for restore
        var entity = await _context.TrainingData
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
            
        if (entity != null && entity.IsDeleted)
        {
            entity.Restore(restoredBy);
            await UpdateAsync(entity, cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(string content, TrainingDataType type, CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .AnyAsync(x => x.Content == content && x.Type == type, cancellationToken);
    }

    public async Task<Dictionary<TrainingDataType, int>> GetCountByTypeAsync(CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .GroupBy(x => x.Type)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<IEnumerable<string>> GetAllTagsAsync(CancellationToken cancellationToken = default)
    {
        // This is a simplified version - in a real implementation, you might want to use raw SQL
        // to properly extract tags from the JSON array
        var allTrainingData = await _context.TrainingData
            .Select(x => x.Tags)
            .ToListAsync(cancellationToken);

        return allTrainingData
            .SelectMany(tags => tags)
            .Distinct()
            .OrderBy(tag => tag)
            .ToList();
    }

    public async Task<IEnumerable<string>> GetAllSchemaNamesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .Where(x => x.SchemaName != null)
            .Select(x => x.SchemaName!)
            .Distinct()
            .OrderBy(name => name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<string>> GetTableNamesForSchemaAsync(string schemaName, CancellationToken cancellationToken = default)
    {
        return await _context.TrainingData
            .Where(x => x.SchemaName == schemaName && x.TableName != null)
            .Select(x => x.TableName!)
            .Distinct()
            .OrderBy(name => name)
            .ToListAsync(cancellationToken);
    }

    public async Task UpdateUsageAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            entity.RecordUsage();
            await UpdateAsync(entity, cancellationToken);
        }
    }

    public async Task UpdateQualityScoresAsync(Dictionary<Guid, double> qualityScores, string updatedBy, CancellationToken cancellationToken = default)
    {
        var ids = qualityScores.Keys.ToList();
        var entities = await _context.TrainingData
            .Where(x => ids.Contains(x.Id))
            .ToListAsync(cancellationToken);

        foreach (var entity in entities)
        {
            if (qualityScores.TryGetValue(entity.Id, out var score))
            {
                entity.UpdateQualityScore(score, updatedBy);
            }
        }

        await UpdateRangeAsync(entities, cancellationToken);
    }

    public async Task<IEnumerable<TrainingData>> GetUnusedAsync(TimeSpan unusedPeriod, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow - unusedPeriod;
        
        return await _context.TrainingData
            .Where(x => x.IsActive && 
                       (x.LastUsedAt == null || x.LastUsedAt < cutoffDate))
            .OrderBy(x => x.LastUsedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<TrainingDataStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var totalCount = await _context.TrainingData.CountAsync(cancellationToken);
        var activeCount = await _context.TrainingData.CountAsync(x => x.IsActive, cancellationToken);
        var countByType = await GetCountByTypeAsync(cancellationToken);
        
        var avgQualityScore = await _context.TrainingData
            .Where(x => x.IsActive)
            .AverageAsync(x => x.QualityScore, cancellationToken);
            
        var totalUsageCount = await _context.TrainingData
            .SumAsync(x => x.UsageCount, cancellationToken);
            
        var lastAdded = await _context.TrainingData
            .OrderByDescending(x => x.CreatedAt)
            .Select(x => x.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
            
        var lastUsed = await _context.TrainingData
            .Where(x => x.LastUsedAt != null)
            .OrderByDescending(x => x.LastUsedAt)
            .Select(x => x.LastUsedAt)
            .FirstOrDefaultAsync(cancellationToken);

        var uniqueTagCount = (await GetAllTagsAsync(cancellationToken)).Count();
        var uniqueSchemaCount = (await GetAllSchemaNamesAsync(cancellationToken)).Count();
        
        var uniqueTableCount = await _context.TrainingData
            .Where(x => x.TableName != null)
            .Select(x => x.TableName)
            .Distinct()
            .CountAsync(cancellationToken);

        return new TrainingDataStatistics
        {
            TotalCount = totalCount,
            ActiveCount = activeCount,
            CountByType = countByType,
            AverageQualityScore = avgQualityScore,
            TotalUsageCount = totalUsageCount,
            LastAddedAt = lastAdded == default ? null : lastAdded,
            LastUsedAt = lastUsed,
            UniqueTagCount = uniqueTagCount,
            UniqueSchemaCount = uniqueSchemaCount,
            UniqueTableCount = uniqueTableCount
        };
    }
}
