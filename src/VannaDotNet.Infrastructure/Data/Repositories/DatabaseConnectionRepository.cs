using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Enums;
using VannaDotNet.Domain.Repositories;

namespace VannaDotNet.Infrastructure.Data.Repositories;

/// <summary>
/// Entity Framework implementation of IDatabaseConnectionRepository
/// </summary>
public class DatabaseConnectionRepository : IDatabaseConnectionRepository
{
    private readonly ApplicationDbContext _context;

    public DatabaseConnectionRepository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<DatabaseConnection?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<DatabaseConnection>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DatabaseConnection>> GetActiveAsync(CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .Where(x => x.IsActive)
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DatabaseConnection>> GetByTypeAsync(DatabaseType type, CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .Where(x => x.Type == type)
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DatabaseConnection>> GetByEnvironmentAsync(string environment, CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .Where(x => x.Environment == environment.ToLowerInvariant())
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<DatabaseConnection?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .FirstOrDefaultAsync(x => x.Name == name, cancellationToken);
    }

    public async Task<(IEnumerable<DatabaseConnection> Items, int TotalCount)> GetPaginatedAsync(
        int page,
        int pageSize,
        DatabaseType? type = null,
        bool? isActive = null,
        string? environment = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.DatabaseConnections.AsQueryable();

        // Apply filters
        if (type.HasValue)
        {
            query = query.Where(x => x.Type == type.Value);
        }

        if (isActive.HasValue)
        {
            query = query.Where(x => x.IsActive == isActive.Value);
        }

        if (!string.IsNullOrEmpty(environment))
        {
            query = query.Where(x => x.Environment == environment.ToLowerInvariant());
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(x => EF.Functions.ILike(x.Name, $"%{searchTerm}%") ||
                                   (x.Description != null && EF.Functions.ILike(x.Description, $"%{searchTerm}%")) ||
                                   EF.Functions.ILike(x.Host, $"%{searchTerm}%"));
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and ordering
        var items = await query
            .OrderBy(x => x.Name)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<IEnumerable<DatabaseConnection>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .Where(x => EF.Functions.ILike(x.Name, $"%{searchTerm}%") ||
                       (x.Description != null && EF.Functions.ILike(x.Description, $"%{searchTerm}%")) ||
                       EF.Functions.ILike(x.Host, $"%{searchTerm}%"))
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DatabaseConnection>> GetNeedingTestAsync(TimeSpan testInterval, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow - testInterval;
        
        return await _context.DatabaseConnections
            .Where(x => x.IsActive && 
                       (x.LastTestedAt == null || x.LastTestedAt < cutoffTime))
            .OrderBy(x => x.LastTestedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DatabaseConnection>> GetWithFailedTestsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .Where(x => x.IsActive && x.LastTestResult == false)
            .OrderByDescending(x => x.LastTestedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DatabaseConnection>> GetHighUsageAsync(int limit, CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .Where(x => x.IsActive)
            .OrderByDescending(x => x.SuccessfulQueries + x.FailedQueries)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DatabaseConnection>> GetLowSuccessRateAsync(double maxSuccessRate, CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .Where(x => x.IsActive && 
                       (x.SuccessfulQueries + x.FailedQueries) > 0 &&
                       (double)x.SuccessfulQueries / (x.SuccessfulQueries + x.FailedQueries) <= maxSuccessRate)
            .OrderBy(x => (double)x.SuccessfulQueries / (x.SuccessfulQueries + x.FailedQueries))
            .ToListAsync(cancellationToken);
    }

    public async Task<DatabaseConnection> AddAsync(DatabaseConnection databaseConnection, CancellationToken cancellationToken = default)
    {
        _context.DatabaseConnections.Add(databaseConnection);
        await _context.SaveChangesAsync(cancellationToken);
        return databaseConnection;
    }

    public async Task UpdateAsync(DatabaseConnection databaseConnection, CancellationToken cancellationToken = default)
    {
        _context.DatabaseConnections.Update(databaseConnection);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            _context.DatabaseConnections.Remove(entity);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task SoftDeleteAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            entity.Delete(deletedBy);
            await UpdateAsync(entity, cancellationToken);
        }
    }

    public async Task<bool> NameExistsAsync(string name, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _context.DatabaseConnections.Where(x => x.Name == name);
        
        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    public async Task<DatabaseConnectionStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var totalConnections = await _context.DatabaseConnections.CountAsync(cancellationToken);
        var activeConnections = await _context.DatabaseConnections.CountAsync(x => x.IsActive, cancellationToken);
        var inactiveConnections = totalConnections - activeConnections;

        var connectionsByType = await _context.DatabaseConnections
            .GroupBy(x => x.Type)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);

        var connectionsByEnvironment = await _context.DatabaseConnections
            .GroupBy(x => x.Environment)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);

        var connectionsWithFailedTests = await _context.DatabaseConnections
            .CountAsync(x => x.LastTestResult == false, cancellationToken);

        var connectionsNeedingTest = await _context.DatabaseConnections
            .CountAsync(x => x.IsActive && x.LastTestedAt == null, cancellationToken);

        var avgSuccessRate = await _context.DatabaseConnections
            .Where(x => x.SuccessfulQueries + x.FailedQueries > 0)
            .AverageAsync(x => (double)x.SuccessfulQueries / (x.SuccessfulQueries + x.FailedQueries), cancellationToken);

        var avgExecutionTime = await _context.DatabaseConnections
            .Where(x => x.SuccessfulQueries > 0)
            .AverageAsync(x => x.TotalExecutionTime.TotalMilliseconds / x.SuccessfulQueries, cancellationToken);

        var totalQueries = await _context.DatabaseConnections
            .SumAsync(x => x.SuccessfulQueries + x.FailedQueries, cancellationToken);

        var totalSuccessfulQueries = await _context.DatabaseConnections
            .SumAsync(x => x.SuccessfulQueries, cancellationToken);

        var totalFailedQueries = await _context.DatabaseConnections
            .SumAsync(x => x.FailedQueries, cancellationToken);

        return new DatabaseConnectionStatistics
        {
            TotalConnections = totalConnections,
            ActiveConnections = activeConnections,
            InactiveConnections = inactiveConnections,
            ConnectionsByType = connectionsByType,
            ConnectionsByEnvironment = connectionsByEnvironment,
            ConnectionsWithFailedTests = connectionsWithFailedTests,
            ConnectionsNeedingTest = connectionsNeedingTest,
            AverageSuccessRate = avgSuccessRate,
            AverageExecutionTime = TimeSpan.FromMilliseconds(avgExecutionTime),
            TotalQueries = totalQueries,
            TotalSuccessfulQueries = totalSuccessfulQueries,
            TotalFailedQueries = totalFailedQueries
        };
    }

    public async Task<DatabaseUsageStatistics> GetUsageStatisticsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var connection = await GetByIdAsync(id, cancellationToken);
        if (connection == null)
        {
            throw new ArgumentException($"Database connection with ID {id} not found");
        }

        var successRate = connection.SuccessfulQueries + connection.FailedQueries > 0
            ? (double)connection.SuccessfulQueries / (connection.SuccessfulQueries + connection.FailedQueries)
            : 0.0;

        var avgExecutionTime = connection.SuccessfulQueries > 0
            ? TimeSpan.FromTicks(connection.TotalExecutionTime.Ticks / connection.SuccessfulQueries)
            : TimeSpan.Zero;

        // Get query sessions for this connection to calculate additional metrics
        var querySessions = await _context.QuerySessions
            .Where(x => x.DatabaseConnectionId == id && x.ExecutionTime.HasValue)
            .Select(x => x.ExecutionTime!.Value)
            .ToListAsync(cancellationToken);

        var fastestQuery = querySessions.Any() ? querySessions.Min() : TimeSpan.Zero;
        var slowestQuery = querySessions.Any() ? querySessions.Max() : TimeSpan.Zero;

        return new DatabaseUsageStatistics
        {
            DatabaseConnectionId = id,
            DatabaseName = connection.DatabaseName,
            TotalQueries = connection.SuccessfulQueries + connection.FailedQueries,
            SuccessfulQueries = connection.SuccessfulQueries,
            FailedQueries = connection.FailedQueries,
            SuccessRate = successRate,
            TotalExecutionTime = connection.TotalExecutionTime,
            AverageExecutionTime = avgExecutionTime,
            FastestQuery = fastestQuery,
            SlowestQuery = slowestQuery,
            LastUsed = await _context.QuerySessions
                .Where(x => x.DatabaseConnectionId == id)
                .OrderByDescending(x => x.CreatedAt)
                .Select(x => x.CreatedAt)
                .FirstOrDefaultAsync(cancellationToken),
            LastTested = connection.LastTestedAt,
            LastTestResult = connection.LastTestResult,
            QueriesByHour = new Dictionary<string, int>(), // TODO: Implement hourly statistics
            QueriesByDay = new Dictionary<string, int>(),  // TODO: Implement daily statistics
            MostCommonErrors = new List<string>()          // TODO: Implement error analysis
        };
    }

    public async Task UpdateTestResultAsync(Guid id, bool success, string? errorMessage = null, CancellationToken cancellationToken = default)
    {
        var connection = await GetByIdAsync(id, cancellationToken);
        if (connection != null)
        {
            connection.RecordTestResult(success, errorMessage);
            await UpdateAsync(connection, cancellationToken);
        }
    }

    public async Task RecordQueryExecutionAsync(Guid id, bool success, TimeSpan? executionTime = null, CancellationToken cancellationToken = default)
    {
        var connection = await GetByIdAsync(id, cancellationToken);
        if (connection != null)
        {
            if (success && executionTime.HasValue)
            {
                connection.RecordSuccessfulQuery(executionTime.Value);
            }
            else
            {
                connection.RecordFailedQuery();
            }
            
            await UpdateAsync(connection, cancellationToken);
        }
    }

    public async Task<IEnumerable<string>> GetEnvironmentsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .Select(x => x.Environment)
            .Distinct()
            .OrderBy(x => x)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DatabaseConnection>> GetByHostAsync(string host, CancellationToken cancellationToken = default)
    {
        return await _context.DatabaseConnections
            .Where(x => x.Host == host)
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task BulkUpdateStatusAsync(IEnumerable<Guid> ids, bool isActive, string updatedBy, CancellationToken cancellationToken = default)
    {
        var connections = await _context.DatabaseConnections
            .Where(x => ids.Contains(x.Id))
            .ToListAsync(cancellationToken);

        foreach (var connection in connections)
        {
            if (isActive)
            {
                connection.Activate(updatedBy);
            }
            else
            {
                connection.Deactivate(updatedBy);
            }
        }

        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<IEnumerable<DatabaseConnection>> GetUnusedAsync(TimeSpan unusedPeriod, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow - unusedPeriod;

        // Get connections that haven't been used in query sessions
        var usedConnectionIds = await _context.QuerySessions
            .Where(x => x.CreatedAt >= cutoffDate)
            .Select(x => x.DatabaseConnectionId)
            .Distinct()
            .ToListAsync(cancellationToken);

        return await _context.DatabaseConnections
            .Where(x => x.IsActive && !usedConnectionIds.Contains(x.Id))
            .OrderBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }
}
