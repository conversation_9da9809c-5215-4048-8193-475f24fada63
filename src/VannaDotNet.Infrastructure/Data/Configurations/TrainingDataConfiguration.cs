using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Enums;

namespace VannaDotNet.Infrastructure.Data.Configurations;

/// <summary>
/// Entity Framework configuration for TrainingData entity
/// </summary>
public class TrainingDataConfiguration : IEntityTypeConfiguration<TrainingData>
{
    public void Configure(EntityTypeBuilder<TrainingData> builder)
    {
        // Table configuration
        builder.ToTable("training_data");

        // Primary key
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id)
            .HasColumnName("id")
            .ValueGeneratedNever(); // Domain generates the ID

        // Content
        builder.Property(x => x.Content)
            .HasColumnName("content")
            .HasMaxLength(10000)
            .IsRequired();

        // Type
        builder.Property(x => x.Type)
            .HasColumnName("type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        // Description
        builder.Property(x => x.Description)
            .HasColumnName("description")
            .HasMaxLength(1000);

        // Tags - stored as JSON array
        builder.Property(x => x.Tags)
            .HasColumnName("tags")
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
            .HasColumnType("jsonb");

        // Question (for QuestionSql type)
        builder.Property(x => x.Question)
            .HasColumnName("question")
            .HasMaxLength(2000);

        // SQL Query (for QuestionSql type)
        builder.Property(x => x.SqlQuery)
            .HasColumnName("sql_query")
            .HasMaxLength(5000);

        // Schema and table names
        builder.Property(x => x.SchemaName)
            .HasColumnName("schema_name")
            .HasMaxLength(100);

        builder.Property(x => x.TableName)
            .HasColumnName("table_name")
            .HasMaxLength(100);

        // Status and quality
        builder.Property(x => x.IsActive)
            .HasColumnName("is_active")
            .HasDefaultValue(true);

        builder.Property(x => x.QualityScore)
            .HasColumnName("quality_score")
            .HasPrecision(3, 2)
            .HasDefaultValue(1.0);

        // Usage tracking
        builder.Property(x => x.UsageCount)
            .HasColumnName("usage_count")
            .HasDefaultValue(0);

        builder.Property(x => x.LastUsedAt)
            .HasColumnName("last_used_at")
            .HasColumnType("timestamp with time zone");

        // Audit fields from BaseEntity
        builder.Property(x => x.CreatedAt)
            .HasColumnName("created_at")
            .HasColumnType("timestamp with time zone")
            .IsRequired();

        builder.Property(x => x.UpdatedAt)
            .HasColumnName("updated_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.CreatedBy)
            .HasColumnName("created_by")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(x => x.UpdatedBy)
            .HasColumnName("updated_by")
            .HasMaxLength(100);

        // Soft delete
        builder.Property(x => x.IsDeleted)
            .HasColumnName("is_deleted")
            .HasDefaultValue(false);

        builder.Property(x => x.DeletedAt)
            .HasColumnName("deleted_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.DeletedBy)
            .HasColumnName("deleted_by")
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(x => x.Type)
            .HasDatabaseName("ix_training_data_type");

        builder.HasIndex(x => x.IsActive)
            .HasDatabaseName("ix_training_data_is_active");

        builder.HasIndex(x => x.QualityScore)
            .HasDatabaseName("ix_training_data_quality_score");

        builder.HasIndex(x => x.CreatedAt)
            .HasDatabaseName("ix_training_data_created_at");

        builder.HasIndex(x => x.SchemaName)
            .HasDatabaseName("ix_training_data_schema_name")
            .HasFilter("schema_name IS NOT NULL");

        builder.HasIndex(x => x.TableName)
            .HasDatabaseName("ix_training_data_table_name")
            .HasFilter("table_name IS NOT NULL");

        // GIN index for tags (PostgreSQL specific)
        builder.HasIndex(x => x.Tags)
            .HasDatabaseName("ix_training_data_tags_gin")
            .HasMethod("gin");

        // Full-text search index for content (PostgreSQL specific)
        builder.HasIndex("to_tsvector('english', content)")
            .HasDatabaseName("ix_training_data_content_fts");

        // Query filter for soft delete
        builder.HasQueryFilter(x => !x.IsDeleted);
    }
}
