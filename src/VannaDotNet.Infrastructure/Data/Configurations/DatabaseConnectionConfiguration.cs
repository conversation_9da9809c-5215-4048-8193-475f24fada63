using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Enums;

namespace VannaDotNet.Infrastructure.Data.Configurations;

/// <summary>
/// Entity Framework configuration for DatabaseConnection entity
/// </summary>
public class DatabaseConnectionConfiguration : IEntityTypeConfiguration<DatabaseConnection>
{
    public void Configure(EntityTypeBuilder<DatabaseConnection> builder)
    {
        // Table configuration
        builder.ToTable("database_connections");

        // Primary key
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id)
            .HasColumnName("id")
            .ValueGeneratedNever(); // Domain generates the ID

        // Name
        builder.Property(x => x.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        // Type
        builder.Property(x => x.Type)
            .HasColumnName("type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        // Connection string (encrypted)
        builder.Property(x => x.ConnectionString)
            .HasColumnName("connection_string")
            .HasMaxLength(1000)
            .IsRequired();

        // Description
        builder.Property(x => x.Description)
            .HasColumnName("description")
            .HasMaxLength(1000);

        // Connection details
        builder.Property(x => x.Host)
            .HasColumnName("host")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(x => x.Port)
            .HasColumnName("port")
            .IsRequired();

        builder.Property(x => x.DatabaseName)
            .HasColumnName("database_name")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(x => x.Username)
            .HasColumnName("username")
            .HasMaxLength(100)
            .IsRequired();

        // Configuration
        builder.Property(x => x.IsReadOnly)
            .HasColumnName("is_read_only")
            .HasDefaultValue(true);

        builder.Property(x => x.IsActive)
            .HasColumnName("is_active")
            .HasDefaultValue(true);

        builder.Property(x => x.MaxConnections)
            .HasColumnName("max_connections")
            .HasDefaultValue(10);

        builder.Property(x => x.TimeoutSeconds)
            .HasColumnName("timeout_seconds")
            .HasDefaultValue(30);

        builder.Property(x => x.QueryTimeoutSeconds)
            .HasColumnName("query_timeout_seconds")
            .HasDefaultValue(60);

        builder.Property(x => x.MaxRowLimit)
            .HasColumnName("max_row_limit")
            .HasDefaultValue(10000);

        // Allowed schemas - stored as JSON array
        builder.Property(x => x.AllowedSchemas)
            .HasColumnName("allowed_schemas")
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
            .HasColumnType("jsonb");

        // Test results
        builder.Property(x => x.LastTestedAt)
            .HasColumnName("last_tested_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.LastTestResult)
            .HasColumnName("last_test_result");

        builder.Property(x => x.LastTestError)
            .HasColumnName("last_test_error")
            .HasMaxLength(1000);

        // Usage statistics
        builder.Property(x => x.SuccessfulQueries)
            .HasColumnName("successful_queries")
            .HasDefaultValue(0L);

        builder.Property(x => x.FailedQueries)
            .HasColumnName("failed_queries")
            .HasDefaultValue(0L);

        builder.Property(x => x.TotalExecutionTime)
            .HasColumnName("total_execution_time")
            .HasConversion(
                v => v.TotalMilliseconds,
                v => TimeSpan.FromMilliseconds(v))
            .HasDefaultValue(0.0);

        // Environment
        builder.Property(x => x.Environment)
            .HasColumnName("environment")
            .HasMaxLength(50)
            .HasDefaultValue("development");

        // Audit fields from BaseEntity
        builder.Property(x => x.CreatedAt)
            .HasColumnName("created_at")
            .HasColumnType("timestamp with time zone")
            .IsRequired();

        builder.Property(x => x.UpdatedAt)
            .HasColumnName("updated_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.CreatedBy)
            .HasColumnName("created_by")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(x => x.UpdatedBy)
            .HasColumnName("updated_by")
            .HasMaxLength(100);

        // Soft delete
        builder.Property(x => x.IsDeleted)
            .HasColumnName("is_deleted")
            .HasDefaultValue(false);

        builder.Property(x => x.DeletedAt)
            .HasColumnName("deleted_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.DeletedBy)
            .HasColumnName("deleted_by")
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(x => x.Name)
            .HasDatabaseName("ix_database_connections_name")
            .IsUnique();

        builder.HasIndex(x => x.Type)
            .HasDatabaseName("ix_database_connections_type");

        builder.HasIndex(x => x.IsActive)
            .HasDatabaseName("ix_database_connections_is_active");

        builder.HasIndex(x => x.Environment)
            .HasDatabaseName("ix_database_connections_environment");

        builder.HasIndex(x => x.Host)
            .HasDatabaseName("ix_database_connections_host");

        builder.HasIndex(x => x.LastTestedAt)
            .HasDatabaseName("ix_database_connections_last_tested_at")
            .HasFilter("last_tested_at IS NOT NULL");

        // GIN index for allowed schemas (PostgreSQL specific)
        builder.HasIndex(x => x.AllowedSchemas)
            .HasDatabaseName("ix_database_connections_allowed_schemas_gin")
            .HasMethod("gin");

        // Query filter for soft delete
        builder.HasQueryFilter(x => !x.IsDeleted);
    }
}
