using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VannaDotNet.Domain.Entities;
using VannaDotNet.Domain.Enums;

namespace VannaDotNet.Infrastructure.Data.Configurations;

/// <summary>
/// Entity Framework configuration for QuerySession entity
/// </summary>
public class QuerySessionConfiguration : IEntityTypeConfiguration<QuerySession>
{
    public void Configure(EntityTypeBuilder<QuerySession> builder)
    {
        // Table configuration
        builder.ToTable("query_sessions");

        // Primary key
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id)
            .HasColumnName("id")
            .ValueGeneratedNever(); // Domain generates the ID

        // Question
        builder.Property(x => x.Question)
            .HasColumnName("question")
            .HasMaxLength(2000)
            .IsRequired();

        // Generated SQL - stored as the Query property from SqlQuery value object
        builder.Property(x => x.GeneratedSql)
            .HasColumnName("generated_sql")
            .HasMaxLength(10000)
            .HasConversion(
                v => v != null ? v.Query : null,
                v => v != null ? new Domain.ValueObjects.SqlQuery(v) : null);

        // Status
        builder.Property(x => x.Status)
            .HasColumnName("status")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        // Results (JSON)
        builder.Property(x => x.Results)
            .HasColumnName("results")
            .HasColumnType("jsonb");

        // Confidence score
        builder.Property(x => x.Confidence)
            .HasColumnName("confidence")
            .HasPrecision(5, 4);

        // Execution time
        builder.Property(x => x.ExecutionTime)
            .HasColumnName("execution_time")
            .HasConversion(
                v => v.HasValue ? v.Value.TotalMilliseconds : (double?)null,
                v => v.HasValue ? TimeSpan.FromMilliseconds(v.Value) : (TimeSpan?)null);

        // Database connection reference
        builder.Property(x => x.DatabaseConnectionId)
            .HasColumnName("database_connection_id")
            .IsRequired();

        // Error information
        builder.Property(x => x.ErrorMessage)
            .HasColumnName("error_message")
            .HasMaxLength(2000);

        builder.Property(x => x.ErrorStackTrace)
            .HasColumnName("error_stack_trace")
            .HasColumnType("text");

        // Row count
        builder.Property(x => x.RowCount)
            .HasColumnName("row_count");

        // Explanation
        builder.Property(x => x.Explanation)
            .HasColumnName("explanation")
            .HasColumnType("text");

        // Follow-up questions (JSON)
        builder.Property(x => x.FollowUpQuestions)
            .HasColumnName("follow_up_questions")
            .HasColumnType("jsonb");

        // Visualization config (JSON)
        builder.Property(x => x.VisualizationConfig)
            .HasColumnName("visualization_config")
            .HasColumnType("jsonb");

        // User feedback
        builder.Property(x => x.UserRating)
            .HasColumnName("user_rating");

        builder.Property(x => x.UserComments)
            .HasColumnName("user_comments")
            .HasMaxLength(1000);

        // Training usage
        builder.Property(x => x.UsedForTraining)
            .HasColumnName("used_for_training")
            .HasDefaultValue(false);

        // Timing information
        builder.Property(x => x.GenerationStartedAt)
            .HasColumnName("generation_started_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.GenerationCompletedAt)
            .HasColumnName("generation_completed_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.ExecutionStartedAt)
            .HasColumnName("execution_started_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.ExecutionCompletedAt)
            .HasColumnName("execution_completed_at")
            .HasColumnType("timestamp with time zone");

        // Audit fields from BaseEntity
        builder.Property(x => x.CreatedAt)
            .HasColumnName("created_at")
            .HasColumnType("timestamp with time zone")
            .IsRequired();

        builder.Property(x => x.UpdatedAt)
            .HasColumnName("updated_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.CreatedBy)
            .HasColumnName("created_by")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(x => x.UpdatedBy)
            .HasColumnName("updated_by")
            .HasMaxLength(100);

        // Soft delete
        builder.Property(x => x.IsDeleted)
            .HasColumnName("is_deleted")
            .HasDefaultValue(false);

        builder.Property(x => x.DeletedAt)
            .HasColumnName("deleted_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.DeletedBy)
            .HasColumnName("deleted_by")
            .HasMaxLength(100);

        // Foreign key relationship
        builder.HasOne<DatabaseConnection>()
            .WithMany()
            .HasForeignKey(x => x.DatabaseConnectionId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(x => x.Status)
            .HasDatabaseName("ix_query_sessions_status");

        builder.HasIndex(x => x.DatabaseConnectionId)
            .HasDatabaseName("ix_query_sessions_database_connection_id");

        builder.HasIndex(x => x.CreatedAt)
            .HasDatabaseName("ix_query_sessions_created_at");

        builder.HasIndex(x => x.Confidence)
            .HasDatabaseName("ix_query_sessions_confidence")
            .HasFilter("confidence IS NOT NULL");

        builder.HasIndex(x => x.UserRating)
            .HasDatabaseName("ix_query_sessions_user_rating")
            .HasFilter("user_rating IS NOT NULL");

        builder.HasIndex(x => x.UsedForTraining)
            .HasDatabaseName("ix_query_sessions_used_for_training");

        // Full-text search index for question (PostgreSQL specific)
        builder.HasIndex("to_tsvector('english', question)")
            .HasDatabaseName("ix_query_sessions_question_fts");

        // Query filter for soft delete
        builder.HasQueryFilter(x => !x.IsDeleted);
    }
}
