using System.Reflection;
using Microsoft.EntityFrameworkCore;
using VannaDotNet.Application.Common.Interfaces;
using VannaDotNet.Domain.Entities;

namespace VannaDotNet.Infrastructure.Data;

/// <summary>
/// Entity Framework database context for the VannaDotNet application
/// </summary>
public class ApplicationDbContext : DbContext, IApplicationDbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Training data entities
    /// </summary>
    public DbSet<TrainingData> TrainingData { get; set; } = null!;

    /// <summary>
    /// Query session entities
    /// </summary>
    public DbSet<QuerySession> QuerySessions { get; set; } = null!;

    /// <summary>
    /// Database connection entities
    /// </summary>
    public DbSet<DatabaseConnection> DatabaseConnections { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Apply all entity configurations from the current assembly
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        base.OnModelCreating(modelBuilder);
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            // This will be overridden by dependency injection configuration
            optionsBuilder.UseNpgsql("Host=localhost;Database=vannadotnet;Username=postgres;Password=password");
        }

        base.OnConfiguring(optionsBuilder);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update audit fields before saving
        UpdateAuditFields();

        return await base.SaveChangesAsync(cancellationToken);
    }

    public override int SaveChanges()
    {
        // Update audit fields before saving
        UpdateAuditFields();

        return base.SaveChanges();
    }

    /// <summary>
    /// Updates audit fields for entities that implement audit tracking
    /// </summary>
    private void UpdateAuditFields()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is Domain.Common.BaseEntity && 
                       (e.State == EntityState.Added || e.State == EntityState.Modified));

        foreach (var entry in entries)
        {
            var entity = (Domain.Common.BaseEntity)entry.Entity;

            if (entry.State == EntityState.Added)
            {
                // CreatedAt and CreatedBy are set in the domain entity constructor
                // We don't override them here to preserve domain logic
            }
            else if (entry.State == EntityState.Modified)
            {
                // UpdatedAt is handled by the domain entity's SetUpdated method
                // We don't override it here to preserve domain logic
                
                // Ensure CreatedAt and CreatedBy are not modified
                entry.Property(nameof(Domain.Common.BaseEntity.CreatedAt)).IsModified = false;
                entry.Property(nameof(Domain.Common.BaseEntity.CreatedBy)).IsModified = false;
            }
        }
    }
}
