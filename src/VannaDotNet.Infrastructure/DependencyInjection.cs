using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using VannaDotNet.Application.Common.Interfaces;
using VannaDotNet.Domain.Repositories;
using VannaDotNet.Infrastructure.Data;
using VannaDotNet.Infrastructure.Data.Repositories;
using VannaDotNet.Infrastructure.ExternalServices.EmbeddingServices;
using VannaDotNet.Infrastructure.ExternalServices.LlmServices;
using VannaDotNet.Infrastructure.ExternalServices.VectorStores;

namespace VannaDotNet.Infrastructure;

/// <summary>
/// Dependency injection configuration for the Infrastructure layer
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Adds infrastructure layer services to the dependency injection container
    /// </summary>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDatabase(configuration);

        // Repositories
        services.AddRepositories();

        // External Services
        services.AddExternalServices(configuration);

        return services;
    }

    /// <summary>
    /// Adds database services
    /// </summary>
    private static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException("Database connection string 'DefaultConnection' is not configured");
        }

        services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName);
                npgsqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorCodesToAdd: null);
            });

            // Enable sensitive data logging in development
            if (configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
            {
                options.EnableSensitiveDataLogging();
            }

            // Enable detailed errors in development
            if (configuration.GetValue<bool>("Logging:EnableDetailedErrors"))
            {
                options.EnableDetailedErrors();
            }
        });

        // Register the application db context interface
        services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

        return services;
    }

    /// <summary>
    /// Adds repository implementations
    /// </summary>
    private static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        services.AddScoped<ITrainingDataRepository, TrainingDataRepository>();
        services.AddScoped<IQuerySessionRepository, QuerySessionRepository>();
        services.AddScoped<IDatabaseConnectionRepository, DatabaseConnectionRepository>();

        return services;
    }

    /// <summary>
    /// Adds external service implementations
    /// </summary>
    private static IServiceCollection AddExternalServices(this IServiceCollection services, IConfiguration configuration)
    {
        // LLM Services
        services.AddLlmServices(configuration);

        // Embedding Services
        services.AddEmbeddingServices(configuration);

        // Vector Store Services
        services.AddVectorStoreServices(configuration);

        return services;
    }

    /// <summary>
    /// Adds LLM service implementations
    /// </summary>
    private static IServiceCollection AddLlmServices(this IServiceCollection services, IConfiguration configuration)
    {
        var llmProvider = configuration["LLM:Provider"]?.ToLowerInvariant() ?? "openai";

        switch (llmProvider)
        {
            case "openai":
                services.Configure<OpenAiConfiguration>(configuration.GetSection(OpenAiConfiguration.SectionName));
                services.AddScoped<ILlmService, OpenAiService>();
                break;

            case "azure-openai":
                services.Configure<OpenAiConfiguration>(configuration.GetSection("AzureOpenAI"));
                services.AddScoped<ILlmService, OpenAiService>();
                break;

            default:
                throw new InvalidOperationException($"Unsupported LLM provider: {llmProvider}");
        }

        return services;
    }

    /// <summary>
    /// Adds embedding service implementations
    /// </summary>
    private static IServiceCollection AddEmbeddingServices(this IServiceCollection services, IConfiguration configuration)
    {
        var embeddingProvider = configuration["Embedding:Provider"]?.ToLowerInvariant() ?? "openai";

        switch (embeddingProvider)
        {
            case "openai":
                services.Configure<OpenAiEmbeddingConfiguration>(configuration.GetSection(OpenAiEmbeddingConfiguration.SectionName));
                services.AddScoped<IEmbeddingService, OpenAiEmbeddingService>();
                break;

            case "azure-openai":
                services.Configure<OpenAiEmbeddingConfiguration>(configuration.GetSection("AzureOpenAIEmbedding"));
                services.AddScoped<IEmbeddingService, OpenAiEmbeddingService>();
                break;

            default:
                throw new InvalidOperationException($"Unsupported embedding provider: {embeddingProvider}");
        }

        return services;
    }

    /// <summary>
    /// Adds vector store service implementations
    /// </summary>
    private static IServiceCollection AddVectorStoreServices(this IServiceCollection services, IConfiguration configuration)
    {
        var vectorStoreProvider = configuration["VectorStore:Provider"]?.ToLowerInvariant() ?? "inmemory";

        switch (vectorStoreProvider)
        {
            case "inmemory":
                services.AddScoped<IVectorStoreService, InMemoryVectorStoreService>();
                break;

            case "qdrant":
                // TODO: Implement Qdrant service
                throw new NotImplementedException("Qdrant vector store is not yet implemented");

            case "chromadb":
                // TODO: Implement ChromaDB service
                throw new NotImplementedException("ChromaDB vector store is not yet implemented");

            default:
                throw new InvalidOperationException($"Unsupported vector store provider: {vectorStoreProvider}");
        }

        return services;
    }

    /// <summary>
    /// Adds health checks for infrastructure services
    /// </summary>
    public static IServiceCollection AddInfrastructureHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        var healthChecksBuilder = services.AddHealthChecks();

        // Database health check
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        if (!string.IsNullOrEmpty(connectionString))
        {
            healthChecksBuilder.AddNpgSql(connectionString, name: "database");
        }

        // LLM service health check
        healthChecksBuilder.AddCheck<LlmHealthCheck>("llm-service");

        // Embedding service health check
        healthChecksBuilder.AddCheck<EmbeddingHealthCheck>("embedding-service");

        // Vector store health check
        healthChecksBuilder.AddCheck<VectorStoreHealthCheck>("vector-store");

        return services;
    }
}

/// <summary>
/// Health check for LLM service
/// </summary>
public class LlmHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly ILlmService _llmService;

    public LlmHealthCheck(ILlmService llmService)
    {
        _llmService = llmService;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isAvailable = await _llmService.IsAvailableAsync(cancellationToken);
            
            return isAvailable
                ? Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("LLM service is available")
                : Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("LLM service is not available");
        }
        catch (Exception ex)
        {
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("LLM service health check failed", ex);
        }
    }
}

/// <summary>
/// Health check for embedding service
/// </summary>
public class EmbeddingHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IEmbeddingService _embeddingService;

    public EmbeddingHealthCheck(IEmbeddingService embeddingService)
    {
        _embeddingService = embeddingService;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isAvailable = await _embeddingService.IsAvailableAsync(cancellationToken);
            
            return isAvailable
                ? Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Embedding service is available")
                : Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("Embedding service is not available");
        }
        catch (Exception ex)
        {
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("Embedding service health check failed", ex);
        }
    }
}

/// <summary>
/// Health check for vector store service
/// </summary>
public class VectorStoreHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IVectorStoreService _vectorStoreService;

    public VectorStoreHealthCheck(IVectorStoreService vectorStoreService)
    {
        _vectorStoreService = vectorStoreService;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isAvailable = await _vectorStoreService.IsAvailableAsync(cancellationToken);
            
            return isAvailable
                ? Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Vector store service is available")
                : Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("Vector store service is not available");
        }
        catch (Exception ex)
        {
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("Vector store service health check failed", ex);
        }
    }
}
