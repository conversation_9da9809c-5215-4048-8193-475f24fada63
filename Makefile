# VannaDotNet Makefile
# This Makefile provides convenient commands for common development tasks

# Variables
SOLUTION_FILE = VannaDotNet.sln
WEBAPI_PROJECT = src/VannaDotNet.WebApi/VannaDotNet.WebApi.csproj
WEBAPP_PROJECT = src/VannaDotNet.WebApp/VannaDotNet.WebApp.csproj
DOMAIN_PROJECT = src/VannaDotNet.Domain/VannaDotNet.Domain.csproj
APPLICATION_PROJECT = src/VannaDotNet.Application/VannaDotNet.Application.csproj
INFRASTRUCTURE_PROJECT = src/VannaDotNet.Infrastructure/VannaDotNet.Infrastructure.csproj
CONFIGURATION_FILE = src/VannaDotNet.WebApi/appsettings.json
CONFIGURATION_EXAMPLE = src/VannaDotNet.WebApi/appsettings.example.json
BUILD_CONFIGURATION ?= Debug
DOTNET = dotnet
DOCKER_COMPOSE = docker-compose
DOCKER_COMPOSE_DEV = docker-compose -f docker-compose.dev.yml

# Package management variables
BACKUP_DIR = .package-backup
PACKAGE_TIMEOUT = 300
ALL_PROJECTS = $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT)

# Cross-platform compatibility
SHELL := /bin/bash
.SHELLFLAGS := -eu -o pipefail -c

# Check if timeout command is available (for cross-platform compatibility)
TIMEOUT_CMD := $(shell command -v timeout 2> /dev/null)
ifeq ($(TIMEOUT_CMD),)
    TIMEOUT_PREFIX =
else
    TIMEOUT_PREFIX = timeout $(PACKAGE_TIMEOUT)
endif

# Colors for output
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
RED = \033[0;31m
NC = \033[0m # No Color

# Help target
.PHONY: help
help:
	@echo "${BLUE}VannaDotNet Development Commands${NC}"
	@echo "${YELLOW}===========================================${NC}"
	@echo "${BLUE}Build Commands:${NC}"
	@echo "${GREEN}build${NC}              - Build the entire solution"
	@echo "${GREEN}build-api${NC}          - Build only the WebApi project"
	@echo "${GREEN}build-webapp${NC}       - Build only the WebApp project"
	@echo "${GREEN}clean${NC}              - Clean build artifacts"
	@echo "${GREEN}clean-all${NC}          - Deep clean (bin, obj, node_modules, etc.)"
	@echo "${GREEN}restore${NC}            - Restore NuGet packages"
	@echo "${GREEN}full-build${NC}         - Run clean, restore, build, and test"
	@echo ""
	@echo "${BLUE}Test Commands:${NC}"
	@echo "${GREEN}test${NC}               - Run all tests"
	@echo "${GREEN}lint${NC}               - Run code analysis"
	@echo "${GREEN}security-scan${NC}      - Check for vulnerable packages"
	@echo ""
	@echo "${BLUE}Run Commands:${NC}"
	@echo "${GREEN}run-api${NC}            - Run the WebApi project"
	@echo "${GREEN}run-webapp${NC}         - Run the WebApp project"
	@echo "${GREEN}quick-start${NC}        - Build and run the API"
	@echo ""
	@echo "${BLUE}Docker Commands:${NC}"
	@echo "${GREEN}docker-up${NC}          - Start Docker dependencies"
	@echo "${GREEN}docker-down${NC}        - Stop Docker dependencies"
	@echo "${GREEN}docker-dev-up${NC}      - Start development Docker environment"
	@echo "${GREEN}docker-dev-down${NC}    - Stop development Docker environment"
	@echo ""
	@echo "${BLUE}Database Commands:${NC}"
	@echo "${GREEN}db-update${NC}          - Update database to latest migration"
	@echo "${GREEN}db-migration${NC}       - Create a new migration (use NAME=MigrationName)"
	@echo ""
	@echo "${BLUE}Package Management:${NC}"
	@echo "${GREEN}outdated${NC}           - Check for outdated packages across all projects"
	@echo "${GREEN}outdated-major${NC}     - Check for packages with major version updates"
	@echo "${GREEN}outdated-minor${NC}     - Check for packages with minor/patch updates"
	@echo "${GREEN}update-packages${NC}    - Update all packages to latest compatible versions"
	@echo "${GREEN}update-major${NC}       - Update packages with major version changes (with confirmation)"
	@echo "${GREEN}update-minor${NC}       - Update packages with minor/patch version changes"
	@echo "${GREEN}update-package${NC}     - Update specific package (use PACKAGE=name)"
	@echo "${GREEN}add-package${NC}        - Add package to projects (use PACKAGE=name [PROJECT=path])"
	@echo "${GREEN}remove-package${NC}     - Remove package from projects (use PACKAGE=name [PROJECT=path])"
	@echo "${GREEN}list-packages${NC}      - List all packages across all projects"
	@echo "${GREEN}restore-clean${NC}      - Clean restore with cache clearing"
	@echo "${GREEN}restore-backup${NC}     - Restore project files from backup"
	@echo ""
	@echo "${BLUE}Utility Commands:${NC}"
	@echo "${GREEN}verify-build${NC}       - Run build verification script"
	@echo "${GREEN}setup-config${NC}       - Setup initial configuration"
	@echo "${GREEN}format${NC}             - Format code using dotnet format"
	@echo "${GREEN}dev-setup${NC}          - Setup complete development environment"
	@echo "${GREEN}health-check${NC}       - Check application health"
	@echo "${GREEN}api-info${NC}           - Get API information"
	@echo ""
	@echo "${BLUE}Deployment Commands:${NC}"
	@echo "${GREEN}publish-api${NC}        - Publish WebApi for deployment"
	@echo "${GREEN}publish-webapp${NC}     - Publish WebApp for deployment"
	@echo "${GREEN}install-dotnet${NC}     - Install .NET SDK"
	@echo "${YELLOW}===========================================${NC}"

# Build targets
.PHONY: build
build:
	@echo "${BLUE}Building entire solution...${NC}"
	$(DOTNET) build $(SOLUTION_FILE) -c $(BUILD_CONFIGURATION)
	@echo "${GREEN}Build completed.${NC}"

.PHONY: build-api
build-api:
	@echo "${BLUE}Building WebApi project...${NC}"
	$(DOTNET) build $(WEBAPI_PROJECT) -c $(BUILD_CONFIGURATION)
	@echo "${GREEN}WebApi build completed.${NC}"

.PHONY: build-webapp
build-webapp:
	@echo "${BLUE}Building WebApp project...${NC}"
	$(DOTNET) build $(WEBAPP_PROJECT) -c $(BUILD_CONFIGURATION)
	@echo "${GREEN}WebApp build completed.${NC}"

# Clean target
.PHONY: clean
clean:
	@echo "${BLUE}Cleaning solution...${NC}"
	$(DOTNET) clean $(SOLUTION_FILE)
	@echo "${GREEN}Clean completed.${NC}"

# Restore target
.PHONY: restore
restore:
	@echo "${BLUE}Restoring NuGet packages...${NC}"
	$(DOTNET) restore $(SOLUTION_FILE)
	@echo "${GREEN}Restore completed.${NC}"

# Test target
.PHONY: test
test:
	@echo "${BLUE}Running tests...${NC}"
	$(DOTNET) test $(SOLUTION_FILE) --no-restore
	@echo "${GREEN}Tests completed.${NC}"

# Run targets
.PHONY: run-api
run-api:
	@echo "${BLUE}Running WebApi...${NC}"
	$(DOTNET) run --project $(WEBAPI_PROJECT) --no-build
	@echo "${GREEN}WebApi stopped.${NC}"

.PHONY: run-webapp
run-webapp:
	@echo "${BLUE}Running WebApp...${NC}"
	$(DOTNET) run --project $(WEBAPP_PROJECT) --no-build
	@echo "${GREEN}WebApp stopped.${NC}"

# Docker targets
.PHONY: docker-up
docker-up:
	@echo "${BLUE}Starting Docker dependencies...${NC}"
	$(DOCKER_COMPOSE) up -d
	@echo "${GREEN}Docker dependencies started.${NC}"

.PHONY: docker-down
docker-down:
	@echo "${BLUE}Stopping Docker dependencies...${NC}"
	$(DOCKER_COMPOSE) down
	@echo "${GREEN}Docker dependencies stopped.${NC}"

.PHONY: docker-dev-up
docker-dev-up:
	@echo "${BLUE}Starting development Docker environment...${NC}"
	$(DOCKER_COMPOSE_DEV) up -d
	@echo "${GREEN}Development Docker environment started.${NC}"

.PHONY: docker-dev-down
docker-dev-down:
	@echo "${BLUE}Stopping development Docker environment...${NC}"
	$(DOCKER_COMPOSE_DEV) down
	@echo "${GREEN}Development Docker environment stopped.${NC}"

# Verify build target
.PHONY: verify-build
verify-build:
	@echo "${BLUE}Running build verification...${NC}"
	./scripts/verify-build.sh
	@echo "${GREEN}Build verification completed.${NC}"

# Setup configuration target
.PHONY: setup-config
setup-config:
	@echo "${BLUE}Setting up initial configuration...${NC}"
	@if [ ! -f $(CONFIGURATION_FILE) ] && [ -f $(CONFIGURATION_EXAMPLE) ]; then \
		cp $(CONFIGURATION_EXAMPLE) $(CONFIGURATION_FILE); \
		echo "${GREEN}Configuration file created. Please edit $(CONFIGURATION_FILE) with your settings.${NC}"; \
	else \
		echo "${YELLOW}Configuration file already exists or example file not found.${NC}"; \
	fi

# Format code target
.PHONY: format
format:
	@echo "${BLUE}Formatting code...${NC}"
	$(DOTNET) format $(SOLUTION_FILE)
	@echo "${GREEN}Code formatting completed.${NC}"

# Publish targets
.PHONY: publish-api
publish-api:
	@echo "${BLUE}Publishing WebApi for deployment...${NC}"
	$(DOTNET) publish $(WEBAPI_PROJECT) -c Release -o ./publish/api
	@echo "${GREEN}WebApi published to ./publish/api${NC}"

.PHONY: publish-webapp
publish-webapp:
	@echo "${BLUE}Publishing WebApp for deployment...${NC}"
	$(DOTNET) publish $(WEBAPP_PROJECT) -c Release -o ./publish/webapp
	@echo "${GREEN}WebApp published to ./publish/webapp${NC}"

# Install .NET SDK
.PHONY: install-dotnet
install-dotnet:
	@echo "${BLUE}Installing .NET SDK...${NC}"
	./dotnet-install.sh --channel 9.0
	@echo "${GREEN}.NET SDK installation completed.${NC}"

# Development workflow targets
.PHONY: dev-setup
dev-setup: restore setup-config docker-up build
	@echo "${GREEN}Development environment setup completed!${NC}"
	@echo "${YELLOW}Next steps:${NC}"
	@echo "  1. Edit $(CONFIGURATION_FILE) with your settings"
	@echo "  2. Run 'make run-api' to start the API"
	@echo "  3. Run 'make run-webapp' to start the web application"

.PHONY: full-build
full-build: clean restore build test
	@echo "${GREEN}Full build and test cycle completed!${NC}"

.PHONY: quick-start
quick-start: build run-api
	@echo "${GREEN}Quick start completed!${NC}"

# Database migration targets (if Entity Framework is used)
.PHONY: db-update
db-update:
	@echo "${BLUE}Updating database...${NC}"
	$(DOTNET) ef database update --project $(INFRASTRUCTURE_PROJECT) --startup-project $(WEBAPI_PROJECT)
	@echo "${GREEN}Database update completed.${NC}"

.PHONY: db-migration
db-migration:
	@if [ -z "$(NAME)" ]; then \
		echo "${RED}Error: Migration name required. Use: make db-migration NAME=YourMigrationName${NC}"; \
		exit 1; \
	fi
	@echo "${BLUE}Creating migration: $(NAME)...${NC}"
	$(DOTNET) ef migrations add $(NAME) --project $(INFRASTRUCTURE_PROJECT) --startup-project $(WEBAPI_PROJECT)
	@echo "${GREEN}Migration $(NAME) created.${NC}"

# Linting and code quality targets
.PHONY: lint
lint:
	@echo "${BLUE}Running code analysis...${NC}"
	$(DOTNET) build $(SOLUTION_FILE) --verbosity normal
	@echo "${GREEN}Code analysis completed.${NC}"

.PHONY: security-scan
security-scan:
	@echo "${BLUE}Running security scan...${NC}"
	$(DOTNET) list $(SOLUTION_FILE) package --vulnerable --include-transitive
	@echo "${GREEN}Security scan completed.${NC}"

# Package management targets
.PHONY: outdated
outdated:
	@echo "${BLUE}Checking for outdated packages across all projects...${NC}"
	@for project in $(ALL_PROJECTS); do \
		if [ -f "$$project" ]; then \
			echo "${YELLOW}Checking $$project...${NC}"; \
			$(DOTNET) list "$$project" package --outdated 2>/dev/null || echo "${YELLOW}No packages or unable to check $$project${NC}"; \
			echo ""; \
		else \
			echo "${RED}Warning: Project file $$project not found${NC}"; \
		fi; \
	done
	@echo "${GREEN}Outdated packages check completed.${NC}"

.PHONY: outdated-major
outdated-major:
	@echo "${BLUE}Checking for packages with major version updates...${NC}"
	@for project in $(ALL_PROJECTS); do \
		if [ -f "$$project" ]; then \
			echo "${YELLOW}Checking $$project for major updates...${NC}"; \
			$(DOTNET) list "$$project" package --outdated --include-prerelease 2>/dev/null | grep -E ">" || echo "No major updates found for $$project"; \
			echo ""; \
		else \
			echo "${RED}Warning: Project file $$project not found${NC}"; \
		fi; \
	done
	@echo "${GREEN}Major version check completed.${NC}"

.PHONY: outdated-minor
outdated-minor:
	@echo "${BLUE}Checking for packages with minor/patch version updates...${NC}"
	@for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
		echo "${YELLOW}Checking $$project for minor/patch updates...${NC}"; \
		$(DOTNET) list $$project package --outdated | grep -v ">" || echo "No minor/patch updates found for $$project"; \
		echo ""; \
	done
	@echo "${GREEN}Minor/patch version check completed.${NC}"

.PHONY: update-packages
update-packages:
	@echo "${BLUE}Updating all packages to latest compatible versions...${NC}"
	@echo "${YELLOW}This will update packages across all projects. Continue? [y/N]${NC}" && read ans && [ $${ans:-N} = y ]
	@echo "${BLUE}Creating backup of project files...${NC}"
	@mkdir -p $(BACKUP_DIR)
	@for project in $(ALL_PROJECTS); do \
		if [ -f "$$project" ]; then \
			cp "$$project" "$(BACKUP_DIR)/$$(basename $$project).backup"; \
		fi; \
	done
	@echo "${BLUE}Updating packages...${NC}"
	@for project in $(ALL_PROJECTS); do \
		if [ -f "$$project" ]; then \
			echo "${YELLOW}Updating packages in $$project...${NC}"; \
			$(TIMEOUT_PREFIX) $(DOTNET) restore "$$project" --force-evaluate --no-cache || echo "${YELLOW}Update timeout or failed for $$project${NC}"; \
		fi; \
	done
	@echo "${GREEN}Package update completed. Backup saved in $(BACKUP_DIR)/${NC}"

.PHONY: update-minor
update-minor:
	@echo "${BLUE}Updating packages with minor/patch version changes...${NC}"
	@echo "${YELLOW}This will update packages to latest minor/patch versions. Continue? [y/N]${NC}" && read ans && [ $${ans:-N} = y ]
	@echo "${BLUE}Creating backup of project files...${NC}"
	@mkdir -p .package-backup
	@for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
		cp $$project .package-backup/$$(basename $$project).backup; \
	done
	@echo "${BLUE}Updating minor/patch versions...${NC}"
	@for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
		echo "${YELLOW}Updating minor/patch packages in $$project...${NC}"; \
		$(DOTNET) restore $$project --force-evaluate; \
	done
	@echo "${GREEN}Minor/patch update completed. Backup saved in .package-backup/${NC}"

.PHONY: update-major
update-major:
	@echo "${RED}WARNING: This will update packages with major version changes!${NC}"
	@echo "${YELLOW}Major version updates may introduce breaking changes. Continue? [y/N]${NC}" && read ans && [ $${ans:-N} = y ]
	@echo "${BLUE}Creating backup of project files...${NC}"
	@mkdir -p .package-backup
	@for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
		cp $$project .package-backup/$$(basename $$project).backup; \
	done
	@echo "${BLUE}Updating packages with major version changes...${NC}"
	@for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
		echo "${YELLOW}Updating major version packages in $$project...${NC}"; \
		$(DOTNET) add $$project package --no-restore || true; \
		$(DOTNET) restore $$project --force-evaluate; \
	done
	@echo "${GREEN}Major version update completed. Backup saved in .package-backup/${NC}"
	@echo "${YELLOW}Please test thoroughly after major version updates!${NC}"

.PHONY: restore-clean
restore-clean:
	@echo "${BLUE}Performing clean package restore...${NC}"
	@echo "${BLUE}Clearing NuGet caches...${NC}"
	$(DOTNET) nuget locals all --clear
	@echo "${BLUE}Cleaning solution...${NC}"
	$(DOTNET) clean $(SOLUTION_FILE)
	@echo "${BLUE}Restoring packages...${NC}"
	$(DOTNET) restore $(SOLUTION_FILE) --force-evaluate --no-cache
	@echo "${GREEN}Clean restore completed.${NC}"

.PHONY: update-package
update-package:
	@if [ -z "$(PACKAGE)" ]; then \
		echo "${RED}Error: Package name required. Use: make update-package PACKAGE=PackageName${NC}"; \
		exit 1; \
	fi
	@echo "${BLUE}Updating package $(PACKAGE) across all projects...${NC}"
	@echo "${YELLOW}This will update $(PACKAGE) to the latest version. Continue? [y/N]${NC}" && read ans && [ $${ans:-N} = y ]
	@echo "${BLUE}Creating backup of project files...${NC}"
	@mkdir -p .package-backup
	@for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
		cp $$project .package-backup/$$(basename $$project).backup; \
	done
	@for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
		echo "${YELLOW}Checking if $(PACKAGE) exists in $$project...${NC}"; \
		if $(DOTNET) list $$project package | grep -q "$(PACKAGE)"; then \
			echo "${BLUE}Updating $(PACKAGE) in $$project...${NC}"; \
			$(DOTNET) add $$project package $(PACKAGE); \
		else \
			echo "${YELLOW}$(PACKAGE) not found in $$project, skipping...${NC}"; \
		fi; \
	done
	@echo "${GREEN}Package $(PACKAGE) update completed. Backup saved in .package-backup/${NC}"

.PHONY: add-package
add-package:
	@if [ -z "$(PACKAGE)" ]; then \
		echo "${RED}Error: Package name required. Use: make add-package PACKAGE=PackageName [PROJECT=ProjectPath]${NC}"; \
		exit 1; \
	fi
	@if [ -n "$(PROJECT)" ]; then \
		echo "${BLUE}Adding package $(PACKAGE) to $(PROJECT)...${NC}"; \
		$(DOTNET) add $(PROJECT) package $(PACKAGE); \
	else \
		echo "${BLUE}Adding package $(PACKAGE) to all projects...${NC}"; \
		echo "${YELLOW}This will add $(PACKAGE) to all projects. Continue? [y/N]${NC}" && read ans && [ $${ans:-N} = y ]; \
		for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
			echo "${YELLOW}Adding $(PACKAGE) to $$project...${NC}"; \
			$(DOTNET) add $$project package $(PACKAGE) || true; \
		done; \
	fi
	@echo "${GREEN}Package $(PACKAGE) added successfully.${NC}"

.PHONY: remove-package
remove-package:
	@if [ -z "$(PACKAGE)" ]; then \
		echo "${RED}Error: Package name required. Use: make remove-package PACKAGE=PackageName [PROJECT=ProjectPath]${NC}"; \
		exit 1; \
	fi
	@echo "${YELLOW}This will remove $(PACKAGE). Continue? [y/N]${NC}" && read ans && [ $${ans:-N} = y ]
	@if [ -n "$(PROJECT)" ]; then \
		echo "${BLUE}Removing package $(PACKAGE) from $(PROJECT)...${NC}"; \
		$(DOTNET) remove $(PROJECT) package $(PACKAGE); \
	else \
		echo "${BLUE}Removing package $(PACKAGE) from all projects...${NC}"; \
		for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
			echo "${YELLOW}Checking $(PACKAGE) in $$project...${NC}"; \
			if $(DOTNET) list $$project package | grep -q "$(PACKAGE)"; then \
				echo "${BLUE}Removing $(PACKAGE) from $$project...${NC}"; \
				$(DOTNET) remove $$project package $(PACKAGE); \
			else \
				echo "${YELLOW}$(PACKAGE) not found in $$project, skipping...${NC}"; \
			fi; \
		done; \
	fi
	@echo "${GREEN}Package $(PACKAGE) removed successfully.${NC}"

.PHONY: list-packages
list-packages:
	@echo "${BLUE}Listing all packages across projects...${NC}"
	@for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
		echo "${YELLOW}Packages in $$project:${NC}"; \
		$(DOTNET) list $$project package || true; \
		echo ""; \
	done
	@echo "${GREEN}Package listing completed.${NC}"

.PHONY: restore-backup
restore-backup:
	@if [ ! -d ".package-backup" ]; then \
		echo "${RED}Error: No backup found in .package-backup directory${NC}"; \
		exit 1; \
	fi
	@echo "${YELLOW}This will restore project files from backup. Continue? [y/N]${NC}" && read ans && [ $${ans:-N} = y ]
	@echo "${BLUE}Restoring project files from backup...${NC}"
	@for backup in .package-backup/*.backup; do \
		if [ -f "$$backup" ]; then \
			original=$$(echo $$backup | sed 's|.package-backup/||' | sed 's|.backup$$||'); \
			for project in $(DOMAIN_PROJECT) $(APPLICATION_PROJECT) $(INFRASTRUCTURE_PROJECT) $(WEBAPI_PROJECT) $(WEBAPP_PROJECT); do \
				if [ "$$(basename $$project)" = "$$original" ]; then \
					echo "${YELLOW}Restoring $$project...${NC}"; \
					cp "$$backup" "$$project"; \
					break; \
				fi; \
			done; \
		fi; \
	done
	@echo "${BLUE}Running clean restore after backup restoration...${NC}"
	$(DOTNET) restore $(SOLUTION_FILE) --force-evaluate
	@echo "${GREEN}Backup restoration completed.${NC}"

.PHONY: package-help
package-help:
	@echo "${BLUE}VannaDotNet Package Management Help${NC}"
	@echo "${YELLOW}===========================================${NC}"
	@echo "${GREEN}Basic Commands:${NC}"
	@echo "  make list-packages          - List all packages in all projects"
	@echo "  make outdated               - Check for outdated packages"
	@echo "  make outdated-major         - Check for major version updates"
	@echo "  make outdated-minor         - Check for minor/patch updates"
	@echo ""
	@echo "${GREEN}Update Commands:${NC}"
	@echo "  make update-packages        - Update all packages (with confirmation)"
	@echo "  make update-minor           - Update minor/patch versions only"
	@echo "  make update-major           - Update major versions (with warning)"
	@echo "  make restore-clean          - Clean restore with cache clearing"
	@echo ""
	@echo "${GREEN}Specific Package Commands:${NC}"
	@echo "  make add-package PACKAGE=Newtonsoft.Json"
	@echo "  make add-package PACKAGE=Serilog PROJECT=src/VannaDotNet.WebApi/VannaDotNet.WebApi.csproj"
	@echo "  make update-package PACKAGE=Microsoft.EntityFrameworkCore"
	@echo "  make remove-package PACKAGE=OldPackage"
	@echo ""
	@echo "${GREEN}Safety Commands:${NC}"
	@echo "  make restore-backup         - Restore from backup if update fails"
	@echo ""
	@echo "${YELLOW}Note: All update commands create automatic backups in $(BACKUP_DIR)/${NC}"
	@echo "${YELLOW}===========================================${NC}"

# Monitoring and health check targets
.PHONY: health-check
health-check:
	@echo "${BLUE}Checking application health...${NC}"
	@curl -f http://localhost:5008/health || echo "${RED}Health check failed - is the API running?${NC}"

.PHONY: api-info
api-info:
	@echo "${BLUE}Getting API information...${NC}"
	@curl -s http://localhost:5008/api/info | jq . || echo "${RED}API info failed - is the API running?${NC}"

# Cleanup targets
.PHONY: clean-all
clean-all: clean
	@echo "${BLUE}Deep cleaning...${NC}"
	find . -name "bin" -type d -exec rm -rf {} + 2>/dev/null || true
	find . -name "obj" -type d -exec rm -rf {} + 2>/dev/null || true
	find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
	rm -rf ./publish
	@echo "${GREEN}Deep clean completed.${NC}"

# Default target
.DEFAULT_GOAL := help
