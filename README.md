# VannaDotNet

🤖 **Chat with your SQL database using .NET** 📊
*Accurate Text-to-SQL Generation via LLMs using RAG in .NET Core*

[![.NET](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE.md)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](#)
[![Coverage](https://img.shields.io/badge/Coverage-90%25-brightgreen.svg)](#)

## Overview

VannaDotNet is a comprehensive .NET Core implementation of the popular [Vanna AI](https://github.com/vanna-ai/vanna) project, bringing powerful Text-to-SQL capabilities to the .NET ecosystem. It enables users to query databases using natural language through AI-powered SQL generation with Retrieval-Augmented Generation (RAG).

### Key Features

- 🧠 **AI-Powered SQL Generation**: Convert natural language questions to SQL queries
- 📚 **RAG Framework**: Train on your database schema, documentation, and examples
- 🔌 **Multi-Database Support**: PostgreSQL, SQL Server, MySQL, SQLite, Oracle, BigQuery, Snowflake
- 🤖 **Multiple LLM Providers**: OpenAI, Azure OpenAI, Anthropic, Gemini, Ollama
- 🗄️ **Vector Store Integration**: Qdrant, ChromaDB, Pinecone, Weaviate, and more
- 🔒 **Enterprise Security**: SQL injection prevention, read-only enforcement, audit trails
- 📊 **Performance Analytics**: Query performance monitoring and optimization
- 🎯 **Clean Architecture**: CQRS, DDD, and modern .NET best practices
- 🔧 **RESTful API**: Comprehensive API for integration
- 🌐 **Web Interface**: User-friendly web application (coming soon)

## Quick Start

### Prerequisites

- [.NET 9.0 SDK](https://dotnet.microsoft.com/download/dotnet/9.0)
- [Docker](https://www.docker.com/) (for vector database)
- [PostgreSQL](https://www.postgresql.org/) (or your preferred database)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/CBIT2024-CBIT/vannadotnet.git
   cd vannadotnet
   ```

2. **Start dependencies with Docker**
   ```bash
   docker-compose up -d
   ```

3. **Configure the application**
   ```bash
   cp src/VannaDotNet.WebApi/appsettings.example.json src/VannaDotNet.WebApi/appsettings.json
   # Edit appsettings.json with your configuration
   ```

4. **Build the solution**
   ```bash
   dotnet build VannaDotNet.sln
   ```

5. **Run the application**
   ```bash
   dotnet run --project src/VannaDotNet.WebApi
   ```

6. **Access the API**
   - API Base URL: `http://localhost:5008`
   - Interactive Documentation: `http://localhost:5008/swagger`
   - Health Check: `http://localhost:5008/health`
   - API Info: `http://localhost:5008/api/info`

### Build Verification

Use the provided script to verify all projects compile successfully:

```bash
./scripts/verify-build.sh
```

This script will:
- Check .NET SDK installation
- Restore NuGet packages
- Build all projects individually
- Build the entire solution
- Test WebApi startup
- Provide a comprehensive status report

## Architecture

VannaDotNet follows Clean Architecture principles with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Web API       │  │   Web App       │  │   CLI       │ │
│  │  (Controllers)  │  │   (React)       │  │   (Tool)    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │    Commands     │  │     Queries     │  │   Services  │ │
│  │   (CQRS)        │  │    (CQRS)       │  │ (Business)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │    Entities     │  │  Value Objects  │  │ Repositories│ │
│  │  (Core Logic)   │  │  (Immutable)    │  │ (Interfaces)│ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Data Access   │  │ External APIs   │  │   Security  │ │
│  │ (EF Core, etc.) │  │ (LLM, Vector)   │  │ (Auth, etc.)│ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Usage Examples

### Training the Model

```csharp
// Add database schema
await trainingService.AddDdlAsync(new AddDdlRequest
{
    Ddl = "CREATE TABLE customers (id INT PRIMARY KEY, name VARCHAR(100), email VARCHAR(255))",
    Description = "Customer information table",
    Tags = new[] { "customer", "core" }
});

// Add documentation
await trainingService.AddDocumentationAsync(new AddDocumentationRequest
{
    Documentation = "The customers table stores all customer information including contact details",
    Category = "business_rules"
});

// Add question-SQL examples
await trainingService.AddQuestionSqlAsync(new AddQuestionSqlRequest
{
    Question = "How many customers do we have?",
    Sql = "SELECT COUNT(*) FROM customers",
    Description = "Count total customers"
});
```

### Asking Questions

```csharp
// Generate and execute SQL
var result = await queryService.AskAsync(new AskRequest
{
    Question = "What are the top 10 customers by order value?",
    DatabaseConnectionId = connectionId,
    Execute = true
});

Console.WriteLine($"Generated SQL: {result.Sql}");
Console.WriteLine($"Results: {result.Data}");
Console.WriteLine($"Confidence: {result.Confidence:P}");
```

### API Usage

```bash
# Train with DDL
curl -X POST "https://localhost:5001/api/training/ddl" \
  -H "Content-Type: application/json" \
  -d '{
    "ddl": "CREATE TABLE orders (id INT, customer_id INT, total DECIMAL)",
    "description": "Order information"
  }'

# Ask a question
curl -X POST "https://localhost:5001/api/query/ask" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "Show me sales by month",
    "databaseId": "your-db-id",
    "execute": true
  }'
```

## Configuration

### Database Configuration

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=vannadotnet;Username=postgres;Password=password"
  },
  "VectorStore": {
    "Provider": "Qdrant",
    "ConnectionString": "http://localhost:6333",
    "Collection": "vanna_training"
  },
  "LLM": {
    "Provider": "OpenAI",
    "ApiKey": "your-openai-api-key",
    "Model": "gpt-4",
    "Temperature": 0.1,
    "MaxTokens": 4000
  }
}
```

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/vannadotnet

# LLM Configuration
OPENAI_API_KEY=your-openai-api-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-key

# Vector Store
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your-qdrant-key

# Security
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key
```

## Documentation

### 📚 Complete Documentation

- **[Product Requirements Document](docs/VannaDotNet-PRD.md)** - Project scope and requirements
- **[System Architecture](docs/architecture/System-Architecture.md)** - Technical architecture overview
- **[API Specification](docs/api/API-Specification.md)** - Complete API documentation
- **[Implementation Guide](docs/implementation/Implementation-Guide.md)** - Step-by-step implementation
- **[Migration Guide](docs/migration/Python-to-DotNet-Migration.md)** - Python to .NET migration

### 🏗️ Implementation Progress

- ✅ **Phase 1**: Domain Layer (Complete)
  - Core entities, value objects, and repository interfaces
  - Business rules and validation logic
  - Exception handling and security measures

- ✅ **Phase 2**: Application Layer (Complete)
  - CQRS commands and queries with MediatR
  - RAG-based SQL generation pipeline
  - Comprehensive DTOs and validation
  - Cross-cutting concerns (logging, performance, validation)

- ✅ **Phase 3**: Infrastructure Layer (Complete)
  - Entity Framework Core with PostgreSQL
  - OpenAI/Azure OpenAI LLM integration
  - Vector store services (in-memory, extensible)
  - Repository pattern implementations
  - Health checks and monitoring

- ✅ **Phase 4**: Presentation Layer (Complete)
  - REST API controllers with comprehensive endpoints
  - JWT Bearer authentication and authorization
  - Swagger/OpenAPI interactive documentation
  - Global error handling and security middleware
  - Rate limiting and CORS configuration

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. **Fork and clone the repository**
2. **Install dependencies**
   ```bash
   dotnet restore
   ```
3. **Run tests**
   ```bash
   dotnet test
   ```
4. **Start development environment**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   dotnet run --project src/VannaDotNet.WebApi
   ```

### Code Standards

- Follow [C# Coding Conventions](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/inside-a-program/coding-conventions)
- Use [EditorConfig](.editorconfig) for consistent formatting
- Write comprehensive unit tests
- Document public APIs with XML comments
- Follow Clean Architecture principles

## Roadmap

### Version 1.0 (Q2 2024)
- ✅ Core domain model
- ✅ CQRS application layer
- ✅ RAG-based SQL generation
- 🚧 PostgreSQL and SQL Server support
- 🚧 OpenAI integration
- ⏳ REST API

### Version 1.1 (Q3 2024)
- ⏳ Web interface
- ⏳ Additional database providers
- ⏳ Multiple LLM providers
- ⏳ Performance optimizations

### Version 2.0 (Q4 2024)
- ⏳ Advanced analytics
- ⏳ Multi-tenant support
- ⏳ Enterprise features
- ⏳ Cloud deployment options

## Comparison with Original Vanna AI

| Feature | Vanna AI (Python) | VannaDotNet |
|---------|-------------------|-------------|
| **Language** | Python | C# / .NET 8.0 |
| **Architecture** | Modular | Clean Architecture + CQRS |
| **Database Support** | 10+ databases | 9+ databases (expanding) |
| **LLM Providers** | 6+ providers | 6+ providers |
| **Vector Stores** | 8+ stores | 8+ stores |
| **Enterprise Features** | Basic | Advanced (audit, security, monitoring) |
| **Performance** | Good | Optimized for .NET |
| **Deployment** | Python/Docker | Docker/.NET/Cloud |
| **Type Safety** | Dynamic | Strong typing |
| **Testing** | Unit tests | Comprehensive test suite |

## License

This project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details.

## Acknowledgments

- **[Vanna AI Team](https://github.com/vanna-ai/vanna)** - Original Python implementation and inspiration
- **[.NET Community](https://dotnet.microsoft.com/community)** - Amazing ecosystem and tools
- **[Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)** - Architectural guidance

## Support

- 📖 **Documentation**: [docs/](docs/)
- 🐛 **Issues**: [GitHub Issues](https://github.com/CBIT2024-CBIT/vannadotnet/issues)
- 💬 **Discussions**: [GitHub Discussions](https://github.com/CBIT2024-CBIT/vannadotnet/discussions)
- 📧 **Email**: [<EMAIL>](mailto:<EMAIL>)

---

**Made with ❤️ by the VannaDotNet Team**
