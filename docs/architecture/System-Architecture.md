# VannaDotNet System Architecture

## Overview

VannaDotNet follows a Clean Architecture pattern with CQRS (Command Query Responsibility Segregation) to ensure separation of concerns, testability, and maintainability. The system is designed to be modular, scalable, and extensible.

## Architectural Principles

### 1. Clean Architecture
- **Independence**: Business logic is independent of frameworks, UI, and external concerns
- **Testability**: Business logic can be tested without UI, database, or external services
- **Flexibility**: Easy to change UI, database, or external services without affecting business logic
- **Maintainability**: Clear separation of concerns makes the system easier to maintain

### 2. CQRS Pattern
- **Command Side**: Handles write operations (training, configuration)
- **Query Side**: Handles read operations (asking questions, retrieving data)
- **Separation**: Different models for reading and writing operations
- **Scalability**: Independent scaling of read and write operations

### 3. Domain-Driven Design (DDD)
- **Bounded Contexts**: Clear boundaries between different business domains
- **Aggregates**: Consistent business entities with clear boundaries
- **Value Objects**: Immutable objects representing business concepts
- **Domain Services**: Business logic that doesn't belong to entities

## System Layers

### 1. Presentation Layer
```
VannaDotNet.WebApi/
├── Controllers/
│   ├── TrainingController.cs
│   ├── QueryController.cs
│   ├── DatabaseController.cs
│   └── HealthController.cs
├── Middleware/
│   ├── ExceptionHandlingMiddleware.cs
│   ├── AuthenticationMiddleware.cs
│   └── LoggingMiddleware.cs
├── Models/
│   ├── Requests/
│   ├── Responses/
│   └── ViewModels/
└── Program.cs
```

**Responsibilities:**
- HTTP request/response handling
- Input validation and model binding
- Authentication and authorization
- API documentation (Swagger/OpenAPI)
- Error handling and logging

### 2. Application Layer
```
VannaDotNet.Application/
├── Commands/
│   ├── Training/
│   │   ├── AddDdlCommand.cs
│   │   ├── AddDocumentationCommand.cs
│   │   └── AddQuestionSqlCommand.cs
│   └── Database/
│       └── ExecuteQueryCommand.cs
├── Queries/
│   ├── Training/
│   │   └── GetTrainingDataQuery.cs
│   └── Query/
│       └── GenerateSqlQuery.cs
├── Handlers/
│   ├── CommandHandlers/
│   └── QueryHandlers/
├── Services/
│   ├── ILlmService.cs
│   ├── IVectorStoreService.cs
│   ├── IEmbeddingService.cs
│   └── IQueryGenerationService.cs
├── DTOs/
├── Validators/
└── Mappings/
```

**Responsibilities:**
- Business use cases and workflows
- Command and query handling
- Data transfer objects (DTOs)
- Input validation
- Service orchestration

### 3. Domain Layer
```
VannaDotNet.Domain/
├── Entities/
│   ├── TrainingData.cs
│   ├── DatabaseConnection.cs
│   ├── QuerySession.cs
│   └── User.cs
├── ValueObjects/
│   ├── SqlQuery.cs
│   ├── EmbeddingVector.cs
│   └── DatabaseCredentials.cs
├── Aggregates/
│   ├── TrainingDataAggregate.cs
│   └── QuerySessionAggregate.cs
├── Services/
│   ├── IPromptEngineering.cs
│   ├── ISqlValidator.cs
│   └── IQueryOptimizer.cs
├── Repositories/
│   ├── ITrainingDataRepository.cs
│   ├── IDatabaseConnectionRepository.cs
│   └── IQuerySessionRepository.cs
├── Events/
│   ├── TrainingDataAdded.cs
│   ├── QueryGenerated.cs
│   └── QueryExecuted.cs
└── Exceptions/
    ├── DomainException.cs
    ├── ValidationException.cs
    └── SecurityException.cs
```

**Responsibilities:**
- Core business entities and rules
- Domain services and business logic
- Repository interfaces
- Domain events
- Business exceptions

### 4. Infrastructure Layer
```
VannaDotNet.Infrastructure/
├── Data/
│   ├── ApplicationDbContext.cs
│   ├── Repositories/
│   │   ├── TrainingDataRepository.cs
│   │   ├── DatabaseConnectionRepository.cs
│   │   └── QuerySessionRepository.cs
│   ├── Configurations/
│   └── Migrations/
├── ExternalServices/
│   ├── LlmServices/
│   │   ├── OpenAiService.cs
│   │   ├── AzureOpenAiService.cs
│   │   └── OllamaService.cs
│   ├── VectorStores/
│   │   ├── QdrantService.cs
│   │   ├── ChromaDbService.cs
│   │   └── PineconeService.cs
│   ├── DatabaseProviders/
│   │   ├── PostgreSqlProvider.cs
│   │   ├── SqlServerProvider.cs
│   │   └── MySqlProvider.cs
│   └── EmbeddingServices/
│       ├── OpenAiEmbeddingService.cs
│       └── HuggingFaceEmbeddingService.cs
├── Security/
│   ├── EncryptionService.cs
│   ├── KeyVaultService.cs
│   └── AuthenticationService.cs
├── Caching/
│   ├── RedisCacheService.cs
│   └── MemoryCacheService.cs
└── Logging/
    ├── StructuredLogger.cs
    └── TelemetryService.cs
```

**Responsibilities:**
- Data persistence and retrieval
- External service integrations
- Security implementations
- Caching mechanisms
- Logging and monitoring

## Component Interactions

### 1. Training Flow
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant App
    participant Domain
    participant Vector
    participant LLM

    Client->>API: POST /api/train
    API->>App: AddDdlCommand
    App->>Domain: Create TrainingData
    Domain->>App: TrainingData Entity
    App->>Vector: Store Embedding
    App->>LLM: Generate Embedding
    LLM-->>App: Embedding Vector
    Vector-->>App: Success
    App-->>API: Success Response
    API-->>Client: 200 OK
```

### 2. Query Generation Flow
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant App
    participant Domain
    participant Vector
    participant LLM
    participant DB

    Client->>API: POST /api/ask
    API->>App: GenerateSqlQuery
    App->>Vector: Search Similar Context
    Vector-->>App: Related Training Data
    App->>Domain: Build Prompt Context
    Domain-->>App: Prompt Template
    App->>LLM: Generate SQL
    LLM-->>App: SQL Query
    App->>Domain: Validate SQL
    Domain-->>App: Validated SQL
    App->>DB: Execute Query
    DB-->>App: Query Results
    App-->>API: Query Response
    API-->>Client: 200 OK with Results
```

## Data Flow Architecture

### 1. Training Data Pipeline
```
Input Data → Validation → Embedding Generation → Vector Storage → Indexing
     ↓              ↓              ↓                ↓            ↓
  Raw Text    Structured    Vector Embedding    Database    Search Index
```

### 2. Query Processing Pipeline
```
Natural Language → Context Retrieval → Prompt Engineering → LLM Processing → SQL Generation
       ↓                  ↓                 ↓                ↓              ↓
   User Input      Related Context     Structured Prompt   AI Response   Validated SQL
```

### 3. Execution Pipeline
```
Generated SQL → Validation → Security Check → Execution → Result Processing → Response
      ↓             ↓            ↓            ↓              ↓               ↓
   Raw Query    Syntax Check  Permission   Database     Formatted Data   JSON/CSV
```

## Security Architecture

### 1. Authentication & Authorization
- **JWT Tokens**: Stateless authentication
- **Role-Based Access Control (RBAC)**: Fine-grained permissions
- **API Key Management**: Secure external service access
- **OAuth 2.0 Integration**: Third-party authentication

### 2. Data Protection
- **Encryption at Rest**: Database and file encryption
- **Encryption in Transit**: HTTPS/TLS for all communications
- **Key Management**: Azure Key Vault or similar
- **Credential Isolation**: Separate credential storage

### 3. SQL Injection Prevention
- **Query Validation**: Syntax and semantic validation
- **Parameterized Queries**: Safe query execution
- **Query Whitelisting**: Approved query patterns
- **Execution Limits**: Query timeout and resource limits

## Scalability Considerations

### 1. Horizontal Scaling
- **Stateless Design**: No server-side session state
- **Load Balancing**: Multiple application instances
- **Database Sharding**: Distributed data storage
- **Microservices**: Independent service scaling

### 2. Caching Strategy
- **Application Cache**: In-memory caching for frequent data
- **Distributed Cache**: Redis for shared cache
- **Vector Cache**: Embedding result caching
- **Query Cache**: SQL result caching

### 3. Performance Optimization
- **Async Processing**: Non-blocking operations
- **Connection Pooling**: Efficient database connections
- **Batch Processing**: Bulk operations for training
- **CDN Integration**: Static content delivery

## Monitoring and Observability

### 1. Logging
- **Structured Logging**: JSON-formatted logs
- **Correlation IDs**: Request tracing
- **Log Levels**: Appropriate log verbosity
- **Centralized Logging**: ELK stack or similar

### 2. Metrics
- **Application Metrics**: Performance counters
- **Business Metrics**: Query success rates
- **Infrastructure Metrics**: Resource utilization
- **Custom Metrics**: Domain-specific measurements

### 3. Health Checks
- **Liveness Probes**: Application health
- **Readiness Probes**: Service availability
- **Dependency Checks**: External service health
- **Deep Health Checks**: End-to-end validation

## Technology Stack

### Core Framework
- **.NET 8.0**: Latest LTS framework
- **ASP.NET Core**: Web API framework
- **Entity Framework Core**: ORM and data access
- **MediatR**: CQRS implementation

### AI/ML Libraries
- **Microsoft.SemanticKernel**: AI orchestration
- **Azure.AI.OpenAI**: OpenAI integration
- **Microsoft.Extensions.AI**: AI abstraction layer
- **Qdrant.Client**: Vector database client

### Infrastructure
- **PostgreSQL**: Primary database
- **Redis**: Distributed caching
- **Docker**: Containerization
- **Kubernetes**: Container orchestration

This architecture provides a solid foundation for building a scalable, maintainable, and secure VannaDotNet application that can handle enterprise-level requirements while remaining flexible for future enhancements.
