# VannaDotNet Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing VannaDotNet, a .NET Core equivalent of the Vanna AI project. The implementation follows Clean Architecture principles with CQRS pattern and modern .NET best practices.

## Prerequisites

### Development Environment
- **.NET 8.0 SDK** or later
- **Visual Studio 2022** or **VS Code** with C# extension
- **Docker Desktop** for containerized services
- **Git** for version control
- **PostgreSQL** for primary database
- **Redis** for caching (optional)

### External Services
- **OpenAI API Key** or other LLM provider
- **Qdrant** vector database (can be self-hosted)
- **Azure Key Vault** (for production secrets)

## Project Structure Setup

### 1. Solution Structure
Create the solution structure following Clean Architecture:

```
VannaDotNet/
├── src/
│   ├── VannaDotNet.Domain/
│   ├── VannaDotNet.Application/
│   ├── VannaDotNet.Infrastructure/
│   ├── VannaDotNet.WebApi/
│   └── VannaDotNet.WebApp/
├── tests/
│   ├── VannaDotNet.Domain.Tests/
│   ├── VannaDotNet.Application.Tests/
│   ├── VannaDotNet.Infrastructure.Tests/
│   └── VannaDotNet.WebApi.Tests/
├── docs/
├── scripts/
├── docker/
├── VannaDotNet.sln
└── README.md
```

### 2. Create Solution and Projects

```bash
# Create solution
dotnet new sln -n VannaDotNet

# Create projects
dotnet new classlib -n VannaDotNet.Domain -o src/VannaDotNet.Domain
dotnet new classlib -n VannaDotNet.Application -o src/VannaDotNet.Application
dotnet new classlib -n VannaDotNet.Infrastructure -o src/VannaDotNet.Infrastructure
dotnet new webapi -n VannaDotNet.WebApi -o src/VannaDotNet.WebApi
dotnet new react -n VannaDotNet.WebApp -o src/VannaDotNet.WebApp

# Add projects to solution
dotnet sln add src/VannaDotNet.Domain/VannaDotNet.Domain.csproj
dotnet sln add src/VannaDotNet.Application/VannaDotNet.Application.csproj
dotnet sln add src/VannaDotNet.Infrastructure/VannaDotNet.Infrastructure.csproj
dotnet sln add src/VannaDotNet.WebApi/VannaDotNet.WebApi.csproj
dotnet sln add src/VannaDotNet.WebApp/VannaDotNet.WebApp.csproj

# Add project references
dotnet add src/VannaDotNet.Application reference src/VannaDotNet.Domain
dotnet add src/VannaDotNet.Infrastructure reference src/VannaDotNet.Application
dotnet add src/VannaDotNet.WebApi reference src/VannaDotNet.Infrastructure
```

## Phase 1: Domain Layer Implementation

### 1. Core Entities

**TrainingData Entity:**
```csharp
// src/VannaDotNet.Domain/Entities/TrainingData.cs
public class TrainingData : BaseEntity
{
    public string Content { get; private set; }
    public TrainingDataType Type { get; private set; }
    public string? Description { get; private set; }
    public List<string> Tags { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    
    private TrainingData() { } // EF Constructor
    
    public static TrainingData CreateDdl(string ddl, string? description = null, List<string>? tags = null)
    {
        return new TrainingData
        {
            Id = Guid.NewGuid(),
            Content = ddl,
            Type = TrainingDataType.Ddl,
            Description = description,
            Tags = tags ?? new List<string>(),
            CreatedAt = DateTime.UtcNow
        };
    }
}
```

**QuerySession Entity:**
```csharp
// src/VannaDotNet.Domain/Entities/QuerySession.cs
public class QuerySession : BaseEntity
{
    public string Question { get; private set; }
    public string GeneratedSql { get; private set; }
    public QueryStatus Status { get; private set; }
    public string? Results { get; private set; }
    public double? Confidence { get; private set; }
    public TimeSpan? ExecutionTime { get; private set; }
    public string DatabaseConnectionId { get; private set; }
    
    public void MarkAsExecuted(string results, TimeSpan executionTime)
    {
        Results = results;
        ExecutionTime = executionTime;
        Status = QueryStatus.Executed;
    }
}
```

### 2. Value Objects

**SqlQuery Value Object:**
```csharp
// src/VannaDotNet.Domain/ValueObjects/SqlQuery.cs
public record SqlQuery
{
    public string Query { get; }
    public SqlQueryType Type { get; }
    
    public SqlQuery(string query)
    {
        if (string.IsNullOrWhiteSpace(query))
            throw new ArgumentException("SQL query cannot be empty", nameof(query));
            
        Query = query.Trim();
        Type = DetermineQueryType(Query);
        
        ValidateQuery();
    }
    
    private SqlQueryType DetermineQueryType(string query)
    {
        var upperQuery = query.ToUpperInvariant().TrimStart();
        
        return upperQuery switch
        {
            var q when q.StartsWith("SELECT") => SqlQueryType.Select,
            var q when q.StartsWith("INSERT") => SqlQueryType.Insert,
            var q when q.StartsWith("UPDATE") => SqlQueryType.Update,
            var q when q.StartsWith("DELETE") => SqlQueryType.Delete,
            var q when q.StartsWith("CREATE") => SqlQueryType.Create,
            _ => SqlQueryType.Unknown
        };
    }
    
    private void ValidateQuery()
    {
        // Basic SQL injection prevention
        var dangerousPatterns = new[] { ";--", "/*", "*/", "xp_", "sp_" };
        
        if (dangerousPatterns.Any(pattern => Query.Contains(pattern, StringComparison.OrdinalIgnoreCase)))
        {
            throw new SecurityException("Potentially dangerous SQL pattern detected");
        }
    }
}
```

### 3. Repository Interfaces

```csharp
// src/VannaDotNet.Domain/Repositories/ITrainingDataRepository.cs
public interface ITrainingDataRepository
{
    Task<TrainingData> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<TrainingData>> GetByTypeAsync(TrainingDataType type, CancellationToken cancellationToken = default);
    Task<IEnumerable<TrainingData>> SearchByTagsAsync(IEnumerable<string> tags, CancellationToken cancellationToken = default);
    Task<TrainingData> AddAsync(TrainingData trainingData, CancellationToken cancellationToken = default);
    Task UpdateAsync(TrainingData trainingData, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}
```

## Phase 2: Application Layer Implementation

### 1. CQRS Commands and Queries

**Add DDL Command:**
```csharp
// src/VannaDotNet.Application/Commands/Training/AddDdlCommand.cs
public record AddDdlCommand(
    string Ddl,
    string? Description,
    List<string>? Tags
) : IRequest<AddDdlCommandResult>;

public record AddDdlCommandResult(Guid Id, string Status);

// Handler
public class AddDdlCommandHandler : IRequestHandler<AddDdlCommand, AddDdlCommandResult>
{
    private readonly ITrainingDataRepository _repository;
    private readonly IVectorStoreService _vectorStore;
    private readonly IEmbeddingService _embeddingService;
    
    public async Task<AddDdlCommandResult> Handle(AddDdlCommand request, CancellationToken cancellationToken)
    {
        // Create domain entity
        var trainingData = TrainingData.CreateDdl(request.Ddl, request.Description, request.Tags);
        
        // Generate embedding
        var embedding = await _embeddingService.GenerateEmbeddingAsync(request.Ddl, cancellationToken);
        
        // Store in vector database
        await _vectorStore.StoreEmbeddingAsync(trainingData.Id, embedding, request.Ddl, cancellationToken);
        
        // Store in primary database
        await _repository.AddAsync(trainingData, cancellationToken);
        
        return new AddDdlCommandResult(trainingData.Id, "Success");
    }
}
```

**Generate SQL Query:**
```csharp
// src/VannaDotNet.Application/Queries/GenerateSqlQuery.cs
public record GenerateSqlQuery(
    string Question,
    Guid DatabaseConnectionId,
    GenerationOptions? Options = null
) : IRequest<GenerateSqlQueryResult>;

public record GenerateSqlQueryResult(
    string Sql,
    string Explanation,
    double Confidence,
    List<string> FollowUpQuestions
);

// Handler
public class GenerateSqlQueryHandler : IRequestHandler<GenerateSqlQuery, GenerateSqlQueryResult>
{
    private readonly IQueryGenerationService _queryService;
    
    public async Task<GenerateSqlQueryResult> Handle(GenerateSqlQuery request, CancellationToken cancellationToken)
    {
        var result = await _queryService.GenerateSqlAsync(
            request.Question,
            request.DatabaseConnectionId,
            request.Options,
            cancellationToken);
            
        return new GenerateSqlQueryResult(
            result.Sql,
            result.Explanation,
            result.Confidence,
            result.FollowUpQuestions);
    }
}
```

### 2. Application Services

**Query Generation Service:**
```csharp
// src/VannaDotNet.Application/Services/IQueryGenerationService.cs
public interface IQueryGenerationService
{
    Task<QueryGenerationResult> GenerateSqlAsync(
        string question,
        Guid databaseConnectionId,
        GenerationOptions? options = null,
        CancellationToken cancellationToken = default);
}

// Implementation in Infrastructure layer
public class QueryGenerationService : IQueryGenerationService
{
    private readonly IVectorStoreService _vectorStore;
    private readonly ILlmService _llmService;
    private readonly IPromptEngineering _promptEngineering;
    
    public async Task<QueryGenerationResult> GenerateSqlAsync(
        string question,
        Guid databaseConnectionId,
        GenerationOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        // 1. Retrieve similar training data
        var similarData = await _vectorStore.SearchSimilarAsync(question, limit: 5, cancellationToken);
        
        // 2. Build context prompt
        var prompt = await _promptEngineering.BuildSqlGenerationPromptAsync(
            question, similarData, databaseConnectionId, cancellationToken);
        
        // 3. Generate SQL using LLM
        var llmResponse = await _llmService.GenerateCompletionAsync(prompt, options, cancellationToken);
        
        // 4. Extract and validate SQL
        var sql = ExtractSqlFromResponse(llmResponse);
        var validatedSql = ValidateSql(sql);
        
        // 5. Generate explanation and follow-up questions
        var explanation = await GenerateExplanationAsync(question, validatedSql, cancellationToken);
        var followUpQuestions = await GenerateFollowUpQuestionsAsync(question, validatedSql, cancellationToken);
        
        return new QueryGenerationResult
        {
            Sql = validatedSql,
            Explanation = explanation,
            Confidence = CalculateConfidence(llmResponse),
            FollowUpQuestions = followUpQuestions
        };
    }
}
```

## Phase 3: Infrastructure Layer Implementation

### 1. Database Configuration

**Entity Framework DbContext:**
```csharp
// src/VannaDotNet.Infrastructure/Data/ApplicationDbContext.cs
public class ApplicationDbContext : DbContext
{
    public DbSet<TrainingData> TrainingData { get; set; }
    public DbSet<QuerySession> QuerySessions { get; set; }
    public DbSet<DatabaseConnection> DatabaseConnections { get; set; }
    
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options) { }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
    }
}

// Entity Configuration
public class TrainingDataConfiguration : IEntityTypeConfiguration<TrainingData>
{
    public void Configure(EntityTypeBuilder<TrainingData> builder)
    {
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Content).IsRequired().HasMaxLength(10000);
        builder.Property(x => x.Type).HasConversion<string>();
        builder.Property(x => x.Tags)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions)null));
    }
}
```

### 2. External Service Integrations

**OpenAI LLM Service:**
```csharp
// src/VannaDotNet.Infrastructure/ExternalServices/LlmServices/OpenAiService.cs
public class OpenAiService : ILlmService
{
    private readonly OpenAIClient _client;
    private readonly ILogger<OpenAiService> _logger;
    
    public async Task<string> GenerateCompletionAsync(
        string prompt,
        GenerationOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        var chatOptions = new ChatCompletionsOptions
        {
            DeploymentName = options?.Model ?? "gpt-4",
            Temperature = (float)(options?.Temperature ?? 0.1),
            MaxTokens = options?.MaxTokens ?? 4000
        };
        
        chatOptions.Messages.Add(new ChatRequestSystemMessage(prompt));
        
        var response = await _client.GetChatCompletionsAsync(chatOptions, cancellationToken);
        
        return response.Value.Choices[0].Message.Content;
    }
}
```

**Qdrant Vector Store Service:**
```csharp
// src/VannaDotNet.Infrastructure/ExternalServices/VectorStores/QdrantService.cs
public class QdrantService : IVectorStoreService
{
    private readonly QdrantClient _client;
    private readonly string _collectionName;
    
    public async Task StoreEmbeddingAsync(
        Guid id,
        float[] embedding,
        string content,
        CancellationToken cancellationToken = default)
    {
        var point = new PointStruct
        {
            Id = id.ToString(),
            Vectors = embedding,
            Payload = new Dictionary<string, Value>
            {
                ["content"] = content,
                ["timestamp"] = DateTime.UtcNow.ToString("O")
            }
        };
        
        await _client.UpsertAsync(_collectionName, new[] { point }, cancellationToken: cancellationToken);
    }
    
    public async Task<IEnumerable<SimilarityResult>> SearchSimilarAsync(
        string query,
        int limit = 5,
        CancellationToken cancellationToken = default)
    {
        // Generate embedding for query
        var queryEmbedding = await _embeddingService.GenerateEmbeddingAsync(query, cancellationToken);
        
        var searchResult = await _client.SearchAsync(
            _collectionName,
            queryEmbedding,
            limit: (uint)limit,
            cancellationToken: cancellationToken);
        
        return searchResult.Select(r => new SimilarityResult
        {
            Id = Guid.Parse(r.Id.ToString()),
            Content = r.Payload["content"].StringValue,
            Score = r.Score
        });
    }
}
```

## Phase 4: Web API Implementation

### 1. Controllers

**Training Controller:**
```csharp
// src/VannaDotNet.WebApi/Controllers/TrainingController.cs
[ApiController]
[Route("api/[controller]")]
public class TrainingController : ControllerBase
{
    private readonly IMediator _mediator;
    
    [HttpPost("ddl")]
    public async Task<ActionResult<ApiResponse<AddDdlCommandResult>>> AddDdl(
        [FromBody] AddDdlRequest request,
        CancellationToken cancellationToken)
    {
        var command = new AddDdlCommand(request.Ddl, request.Description, request.Tags);
        var result = await _mediator.Send(command, cancellationToken);
        
        return Ok(ApiResponse.Success(result));
    }
    
    [HttpGet("data")]
    public async Task<ActionResult<ApiResponse<PagedResult<TrainingDataDto>>>> GetTrainingData(
        [FromQuery] GetTrainingDataRequest request,
        CancellationToken cancellationToken)
    {
        var query = new GetTrainingDataQuery(request.Type, request.Page, request.Limit, request.Tags);
        var result = await _mediator.Send(query, cancellationToken);
        
        return Ok(ApiResponse.Success(result));
    }
}
```

### 2. Dependency Injection Configuration

**Program.cs:**
```csharp
// src/VannaDotNet.WebApi/Program.cs
var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Database
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(AddDdlCommand).Assembly));

// Application Services
builder.Services.AddScoped<IQueryGenerationService, QueryGenerationService>();
builder.Services.AddScoped<IVectorStoreService, QdrantService>();
builder.Services.AddScoped<ILlmService, OpenAiService>();
builder.Services.AddScoped<IEmbeddingService, OpenAiEmbeddingService>();

// Repositories
builder.Services.AddScoped<ITrainingDataRepository, TrainingDataRepository>();

var app = builder.Build();

// Configure pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

app.Run();
```

## Testing Strategy

### 1. Unit Tests
```csharp
// tests/VannaDotNet.Domain.Tests/Entities/TrainingDataTests.cs
public class TrainingDataTests
{
    [Fact]
    public void CreateDdl_WithValidInput_ShouldCreateTrainingData()
    {
        // Arrange
        var ddl = "CREATE TABLE test (id INT)";
        var description = "Test table";
        
        // Act
        var trainingData = TrainingData.CreateDdl(ddl, description);
        
        // Assert
        trainingData.Should().NotBeNull();
        trainingData.Content.Should().Be(ddl);
        trainingData.Type.Should().Be(TrainingDataType.Ddl);
        trainingData.Description.Should().Be(description);
    }
}
```

### 2. Integration Tests
```csharp
// tests/VannaDotNet.WebApi.Tests/Controllers/TrainingControllerTests.cs
public class TrainingControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    
    [Fact]
    public async Task AddDdl_WithValidRequest_ShouldReturnSuccess()
    {
        // Arrange
        var request = new AddDdlRequest
        {
            Ddl = "CREATE TABLE test (id INT)",
            Description = "Test table"
        };
        
        // Act
        var response = await _client.PostAsJsonAsync("/api/training/ddl", request);
        
        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }
}
```

## Deployment Configuration

### 1. Docker Configuration
```dockerfile
# docker/Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/VannaDotNet.WebApi/VannaDotNet.WebApi.csproj", "src/VannaDotNet.WebApi/"]
RUN dotnet restore "src/VannaDotNet.WebApi/VannaDotNet.WebApi.csproj"
COPY . .
WORKDIR "/src/src/VannaDotNet.WebApi"
RUN dotnet build "VannaDotNet.WebApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "VannaDotNet.WebApi.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "VannaDotNet.WebApi.dll"]
```

### 2. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  vannadotnet-api:
    build:
      context: .
      dockerfile: docker/Dockerfile
    ports:
      - "5000:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=vannadotnet;Username=postgres;Password=password
    depends_on:
      - postgres
      - qdrant
      
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: vannadotnet
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage

volumes:
  postgres_data:
  qdrant_data:
```

This implementation guide provides a comprehensive foundation for building VannaDotNet with modern .NET practices, clean architecture, and production-ready features.
