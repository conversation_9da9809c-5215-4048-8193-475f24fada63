# VannaDotNet API Specification

## Overview

The VannaDotNet API provides RESTful endpoints for training the AI model, generating SQL queries from natural language, and managing database connections. All endpoints follow REST conventions and return JSON responses.

## Base URL
```
https://api.vannadotnet.com/api/v1
```

## Authentication

All API endpoints require authentication using JWT Bearer tokens:

```http
Authorization: Bearer <jwt_token>
```

## Common Response Format

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "uuid-string"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": { ... }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "uuid-string"
}
```

## Training Endpoints

### Add DDL Training Data
Add database schema information for training.

```http
POST /training/ddl
```

**Request Body:**
```json
{
  "ddl": "CREATE TABLE customers (id INT PRIMARY KEY, name VARCHAR(100), email VARCHAR(255))",
  "description": "Customer table schema",
  "tags": ["customer", "schema"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "training-ddl-123",
    "type": "ddl",
    "status": "processed"
  }
}
```

### Add Documentation Training Data
Add business context and documentation.

```http
POST /training/documentation
```

**Request Body:**
```json
{
  "documentation": "Customer table stores all customer information including contact details and preferences",
  "category": "business_rules",
  "tags": ["customer", "documentation"]
}
```

### Add Question-SQL Pairs
Add example question and SQL query pairs.

```http
POST /training/question-sql
```

**Request Body:**
```json
{
  "question": "What are the top 10 customers by order value?",
  "sql": "SELECT c.name, SUM(o.total) as total_value FROM customers c JOIN orders o ON c.id = o.customer_id GROUP BY c.id, c.name ORDER BY total_value DESC LIMIT 10",
  "description": "Top customers by order value",
  "tags": ["customer", "analytics"]
}
```

### Get Training Data
Retrieve existing training data with filtering and pagination.

```http
GET /training/data?type=ddl&page=1&limit=20&tags=customer
```

**Response:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "training-123",
        "type": "ddl",
        "content": "CREATE TABLE...",
        "tags": ["customer", "schema"],
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

### Remove Training Data
Remove specific training data by ID.

```http
DELETE /training/data/{id}
```

## Query Endpoints

### Generate SQL Query
Generate SQL from natural language question.

```http
POST /query/generate
```

**Request Body:**
```json
{
  "question": "Show me the top 5 customers by sales this month",
  "databaseId": "db-connection-123",
  "options": {
    "allowDataIntrospection": false,
    "maxTokens": 4000,
    "temperature": 0.1
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sql": "SELECT c.name, SUM(o.total) as monthly_sales FROM customers c JOIN orders o ON c.id = o.customer_id WHERE o.created_at >= DATE_TRUNC('month', CURRENT_DATE) GROUP BY c.id, c.name ORDER BY monthly_sales DESC LIMIT 5",
    "explanation": "This query joins customers and orders tables, filters for current month, groups by customer, and returns top 5 by sales",
    "confidence": 0.92,
    "executionPlan": {
      "estimatedCost": 150.5,
      "estimatedRows": 5
    }
  }
}
```

### Execute Query
Execute generated SQL query on the database.

```http
POST /query/execute
```

**Request Body:**
```json
{
  "sql": "SELECT * FROM customers LIMIT 10",
  "databaseId": "db-connection-123",
  "options": {
    "maxRows": 1000,
    "timeout": 30
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>"
      }
    ],
    "metadata": {
      "rowCount": 10,
      "executionTime": 0.045,
      "columns": [
        {
          "name": "id",
          "type": "integer",
          "nullable": false
        },
        {
          "name": "name",
          "type": "varchar",
          "nullable": false
        }
      ]
    }
  }
}
```

### Ask Question (Combined)
Generate and optionally execute SQL in one request.

```http
POST /query/ask
```

**Request Body:**
```json
{
  "question": "How many orders were placed last week?",
  "databaseId": "db-connection-123",
  "execute": true,
  "options": {
    "visualize": true,
    "autoTrain": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "question": "How many orders were placed last week?",
    "sql": "SELECT COUNT(*) as order_count FROM orders WHERE created_at >= DATE_TRUNC('week', CURRENT_DATE - INTERVAL '1 week') AND created_at < DATE_TRUNC('week', CURRENT_DATE)",
    "results": [
      {
        "order_count": 245
      }
    ],
    "visualization": {
      "type": "indicator",
      "config": {
        "value": 245,
        "title": "Orders Last Week"
      }
    },
    "followUpQuestions": [
      "How does this compare to the previous week?",
      "What was the total value of these orders?",
      "Which day had the most orders?"
    ]
  }
}
```

## Database Connection Endpoints

### Add Database Connection
Register a new database connection.

```http
POST /databases/connections
```

**Request Body:**
```json
{
  "name": "Production Database",
  "type": "postgresql",
  "connectionString": "encrypted_connection_string",
  "description": "Main production database",
  "settings": {
    "maxConnections": 10,
    "timeout": 30,
    "readOnly": false
  }
}
```

### List Database Connections
Get all available database connections.

```http
GET /databases/connections
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "db-connection-123",
      "name": "Production Database",
      "type": "postgresql",
      "status": "connected",
      "lastTested": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### Test Database Connection
Test connectivity to a database.

```http
POST /databases/connections/{id}/test
```

### Get Database Schema
Retrieve database schema information.

```http
GET /databases/connections/{id}/schema
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tables": [
      {
        "name": "customers",
        "schema": "public",
        "columns": [
          {
            "name": "id",
            "type": "integer",
            "nullable": false,
            "primaryKey": true
          }
        ]
      }
    ]
  }
}
```

## Configuration Endpoints

### Get Configuration
Retrieve current system configuration.

```http
GET /config
```

### Update Configuration
Update system configuration.

```http
PUT /config
```

**Request Body:**
```json
{
  "llm": {
    "provider": "openai",
    "model": "gpt-4",
    "temperature": 0.1,
    "maxTokens": 4000
  },
  "vectorStore": {
    "provider": "qdrant",
    "collection": "vanna_training"
  },
  "security": {
    "allowDataIntrospection": false,
    "maxQueryTimeout": 60
  }
}
```

## Health Check Endpoints

### Basic Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0"
}
```

### Detailed Health Check
```http
GET /health/detailed
```

**Response:**
```json
{
  "status": "healthy",
  "checks": {
    "database": "healthy",
    "vectorStore": "healthy",
    "llmService": "healthy",
    "cache": "healthy"
  },
  "dependencies": [
    {
      "name": "PostgreSQL",
      "status": "healthy",
      "responseTime": 15
    }
  ]
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `INVALID_REQUEST` | Request validation failed |
| `UNAUTHORIZED` | Authentication required |
| `FORBIDDEN` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `RATE_LIMITED` | Too many requests |
| `LLM_ERROR` | LLM service error |
| `DATABASE_ERROR` | Database connection error |
| `VECTOR_STORE_ERROR` | Vector store error |
| `SQL_VALIDATION_ERROR` | Invalid SQL generated |
| `EXECUTION_ERROR` | Query execution failed |

## Rate Limiting

API endpoints are rate limited based on authentication:

- **Free Tier**: 100 requests/hour
- **Pro Tier**: 1000 requests/hour  
- **Enterprise**: Custom limits

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Webhooks

Configure webhooks to receive notifications for events:

### Training Completed
```json
{
  "event": "training.completed",
  "data": {
    "trainingId": "training-123",
    "type": "ddl",
    "status": "success"
  }
}
```

### Query Generated
```json
{
  "event": "query.generated",
  "data": {
    "question": "Show top customers",
    "sql": "SELECT...",
    "confidence": 0.92
  }
}
```

This API specification provides a comprehensive interface for all VannaDotNet functionality while maintaining REST conventions and providing clear documentation for developers.
