# Python to .NET Migration Guide

## Overview

This guide provides detailed mapping and migration strategies for converting the Python-based Vanna AI project to .NET Core. It covers architectural patterns, library equivalents, and implementation approaches specific to the .NET ecosystem.

## Architectural Migration

### Python Vanna Architecture → .NET Architecture

| Python Component | .NET Equivalent | Implementation |
|------------------|-----------------|----------------|
| `vanna.base.VannaBase` | `IVannaService` interface | Abstract service interface |
| Multiple inheritance | Composition pattern | Dependency injection |
| `@abstractmethod` | `abstract` methods | Abstract base classes |
| Duck typing | Strong typing | Interfaces and generics |
| Dynamic imports | Dependency injection | Service registration |

### Design Pattern Migration

**Python Multiple Inheritance:**
```python
# Python - Multiple inheritance
class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, config=config)
```

**.NET Composition Pattern:**
```csharp
// .NET - Composition with dependency injection
public class VannaService : IVannaService
{
    private readonly IVectorStoreService _vectorStore;
    private readonly ILlmService _llmService;
    
    public VannaService(IVectorStoreService vectorStore, ILlmService llmService)
    {
        _vectorStore = vectorStore;
        _llmService = llmService;
    }
}
```

## Core Component Migration

### 1. Base Class Migration

**Python VannaBase:**
```python
class VannaBase(ABC):
    def __init__(self, config=None):
        self.config = config
        self.run_sql_is_set = False
        
    @abstractmethod
    def generate_embedding(self, data: str, **kwargs) -> List[float]:
        pass
        
    def generate_sql(self, question: str, **kwargs) -> str:
        # Implementation
        pass
```

**.NET IVannaService:**
```csharp
public interface IVannaService
{
    Task<float[]> GenerateEmbeddingAsync(string data, CancellationToken cancellationToken = default);
    Task<string> GenerateSqlAsync(string question, GenerationOptions? options = null, CancellationToken cancellationToken = default);
}

public abstract class VannaServiceBase : IVannaService
{
    protected readonly VannaConfiguration _config;
    protected bool _runSqlIsSet;
    
    protected VannaServiceBase(VannaConfiguration config)
    {
        _config = config;
        _runSqlIsSet = false;
    }
    
    public abstract Task<float[]> GenerateEmbeddingAsync(string data, CancellationToken cancellationToken = default);
    
    public virtual async Task<string> GenerateSqlAsync(string question, GenerationOptions? options = null, CancellationToken cancellationToken = default)
    {
        // Implementation
    }
}
```

### 2. Training Methods Migration

**Python Training:**
```python
def train(self, question: str = None, sql: str = None, ddl: str = None, documentation: str = None):
    if ddl:
        return self.add_ddl(ddl)
    if sql:
        if question is None:
            question = self.generate_question(sql)
        return self.add_question_sql(question=question, sql=sql)
    if documentation:
        return self.add_documentation(documentation)
```

**.NET Training Service:**
```csharp
public class TrainingService : ITrainingService
{
    public async Task<TrainingResult> TrainAsync(TrainingRequest request, CancellationToken cancellationToken = default)
    {
        return request switch
        {
            { Ddl: not null } => await AddDdlAsync(request.Ddl, cancellationToken),
            { Sql: not null } => await AddQuestionSqlAsync(
                request.Question ?? await GenerateQuestionAsync(request.Sql, cancellationToken),
                request.Sql,
                cancellationToken),
            { Documentation: not null } => await AddDocumentationAsync(request.Documentation, cancellationToken),
            _ => throw new ArgumentException("Invalid training request")
        };
    }
}
```

### 3. Query Generation Migration

**Python Query Generation:**
```python
def generate_sql(self, question: str, allow_llm_to_see_data=False, **kwargs) -> str:
    question_sql_list = self.get_similar_question_sql(question, **kwargs)
    ddl_list = self.get_related_ddl(question, **kwargs)
    doc_list = self.get_related_documentation(question, **kwargs)
    
    prompt = self.get_sql_prompt(
        question=question,
        question_sql_list=question_sql_list,
        ddl_list=ddl_list,
        doc_list=doc_list,
        **kwargs
    )
    
    llm_response = self.submit_prompt(prompt, **kwargs)
    return self.extract_sql(llm_response)
```

**.NET Query Generation:**
```csharp
public class QueryGenerationService : IQueryGenerationService
{
    public async Task<QueryGenerationResult> GenerateSqlAsync(
        string question,
        GenerationOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        var similarQuestions = await GetSimilarQuestionSqlAsync(question, cancellationToken);
        var relatedDdl = await GetRelatedDdlAsync(question, cancellationToken);
        var relatedDocs = await GetRelatedDocumentationAsync(question, cancellationToken);
        
        var prompt = await BuildSqlPromptAsync(
            question,
            similarQuestions,
            relatedDdl,
            relatedDocs,
            cancellationToken);
        
        var llmResponse = await _llmService.SubmitPromptAsync(prompt, options, cancellationToken);
        var sql = ExtractSql(llmResponse);
        
        return new QueryGenerationResult
        {
            Sql = sql,
            Confidence = CalculateConfidence(llmResponse),
            Explanation = await GenerateExplanationAsync(question, sql, cancellationToken)
        };
    }
}
```

## Library and Framework Migration

### 1. Core Dependencies

| Python Library | .NET Equivalent | Purpose |
|---------------|-----------------|---------|
| `pandas` | `System.Data.DataTable` / Custom DTOs | Data manipulation |
| `requests` | `HttpClient` | HTTP requests |
| `sqlparse` | `Microsoft.SqlServer.TransactSql.ScriptDom` | SQL parsing |
| `plotly` | `Plotly.NET` / `Chart.js` | Data visualization |
| `flask` | `ASP.NET Core` | Web framework |
| `sqlite3` | `Microsoft.Data.Sqlite` | SQLite database |
| `psycopg2` | `Npgsql` | PostgreSQL driver |

### 2. AI/ML Libraries

| Python Library | .NET Equivalent | Purpose |
|---------------|-----------------|---------|
| `openai` | `Azure.AI.OpenAI` | OpenAI API client |
| `chromadb` | `Qdrant.Client` | Vector database |
| `sentence-transformers` | `Microsoft.ML.Tokenizers` | Text embeddings |
| `transformers` | `Microsoft.ML.OnnxRuntime` | Transformer models |

### 3. Data Processing Migration

**Python Pandas DataFrame:**
```python
import pandas as pd

def run_sql(self, sql: str) -> pd.DataFrame:
    results = self.cursor.execute(sql).fetchall()
    columns = [desc[0] for desc in self.cursor.description]
    return pd.DataFrame(results, columns=columns)
```

**.NET DataTable/DTOs:**
```csharp
public class DatabaseService : IDatabaseService
{
    public async Task<QueryResult> RunSqlAsync(string sql, CancellationToken cancellationToken = default)
    {
        using var connection = await CreateConnectionAsync(cancellationToken);
        using var command = new NpgsqlCommand(sql, connection);
        using var reader = await command.ExecuteReaderAsync(cancellationToken);
        
        var results = new List<Dictionary<string, object>>();
        var columns = new List<ColumnInfo>();
        
        // Get column information
        for (int i = 0; i < reader.FieldCount; i++)
        {
            columns.Add(new ColumnInfo
            {
                Name = reader.GetName(i),
                Type = reader.GetFieldType(i).Name,
                Nullable = true // Determine from schema
            });
        }
        
        // Read data
        while (await reader.ReadAsync(cancellationToken))
        {
            var row = new Dictionary<string, object>();
            for (int i = 0; i < reader.FieldCount; i++)
            {
                row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
            }
            results.Add(row);
        }
        
        return new QueryResult
        {
            Data = results,
            Columns = columns,
            RowCount = results.Count
        };
    }
}
```

## Configuration Migration

### Python Configuration
```python
# Python - Dictionary-based config
config = {
    'api_key': 'sk-...',
    'model': 'gpt-4',
    'temperature': 0.7,
    'max_tokens': 4000
}

vn = MyVanna(config=config)
```

### .NET Configuration
```csharp
// .NET - Strongly-typed configuration
public class VannaConfiguration
{
    public string ApiKey { get; set; } = string.Empty;
    public string Model { get; set; } = "gpt-4";
    public double Temperature { get; set; } = 0.7;
    public int MaxTokens { get; set; } = 4000;
}

// appsettings.json
{
  "Vanna": {
    "ApiKey": "sk-...",
    "Model": "gpt-4",
    "Temperature": 0.7,
    "MaxTokens": 4000
  }
}

// Dependency injection
services.Configure<VannaConfiguration>(configuration.GetSection("Vanna"));
```

## Error Handling Migration

### Python Exception Handling
```python
try:
    sql = self.generate_sql(question)
    df = self.run_sql(sql)
    return df
except Exception as e:
    print(f"Error: {e}")
    return None
```

### .NET Exception Handling
```csharp
public async Task<QueryResult> ProcessQueryAsync(string question, CancellationToken cancellationToken = default)
{
    try
    {
        var sql = await GenerateSqlAsync(question, cancellationToken);
        var result = await RunSqlAsync(sql, cancellationToken);
        return result;
    }
    catch (LlmServiceException ex)
    {
        _logger.LogError(ex, "LLM service error while processing question: {Question}", question);
        throw new QueryProcessingException("Failed to generate SQL query", ex);
    }
    catch (DatabaseException ex)
    {
        _logger.LogError(ex, "Database error while executing query for question: {Question}", question);
        throw new QueryExecutionException("Failed to execute SQL query", ex);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Unexpected error while processing question: {Question}", question);
        throw new QueryProcessingException("An unexpected error occurred", ex);
    }
}
```

## Async/Await Migration

### Python Synchronous Code
```python
def ask(self, question: str) -> tuple:
    sql = self.generate_sql(question)
    df = self.run_sql(sql)
    fig = self.generate_plotly_code(question, sql, df)
    return sql, df, fig
```

### .NET Asynchronous Code
```csharp
public async Task<AskResult> AskAsync(string question, CancellationToken cancellationToken = default)
{
    var sql = await GenerateSqlAsync(question, cancellationToken);
    var queryResult = await RunSqlAsync(sql, cancellationToken);
    var visualization = await GenerateVisualizationAsync(question, sql, queryResult, cancellationToken);
    
    return new AskResult
    {
        Sql = sql,
        Data = queryResult,
        Visualization = visualization
    };
}
```

## Testing Migration

### Python Testing
```python
import unittest
from unittest.mock import Mock, patch

class TestVanna(unittest.TestCase):
    def setUp(self):
        self.vanna = MyVanna(config={'api_key': 'test'})
    
    @patch('openai.ChatCompletion.create')
    def test_generate_sql(self, mock_openai):
        mock_openai.return_value = Mock(choices=[Mock(message=Mock(content="SELECT * FROM users"))])
        result = self.vanna.generate_sql("Show all users")
        self.assertEqual(result, "SELECT * FROM users")
```

### .NET Testing
```csharp
public class VannaServiceTests
{
    private readonly Mock<ILlmService> _mockLlmService;
    private readonly Mock<IVectorStoreService> _mockVectorStore;
    private readonly VannaService _vannaService;
    
    public VannaServiceTests()
    {
        _mockLlmService = new Mock<ILlmService>();
        _mockVectorStore = new Mock<IVectorStoreService>();
        _vannaService = new VannaService(_mockLlmService.Object, _mockVectorStore.Object);
    }
    
    [Fact]
    public async Task GenerateSqlAsync_WithValidQuestion_ShouldReturnSql()
    {
        // Arrange
        var question = "Show all users";
        var expectedSql = "SELECT * FROM users";
        
        _mockLlmService
            .Setup(x => x.GenerateCompletionAsync(It.IsAny<string>(), It.IsAny<GenerationOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedSql);
        
        // Act
        var result = await _vannaService.GenerateSqlAsync(question);
        
        // Assert
        result.Should().Be(expectedSql);
    }
}
```

## Performance Considerations

### Python Performance Patterns
- Global Interpreter Lock (GIL) limitations
- Synchronous I/O operations
- Memory management through garbage collection

### .NET Performance Optimizations
- **Async/Await**: Non-blocking I/O operations
- **Memory Pooling**: `ArrayPool<T>` for large arrays
- **Span<T>**: Zero-allocation string operations
- **ValueTask**: Reduced allocations for frequently completed tasks
- **Connection Pooling**: Efficient database connections

```csharp
// Example: Optimized embedding processing
public async Task<float[]> GenerateEmbeddingAsync(string text, CancellationToken cancellationToken = default)
{
    using var activity = ActivitySource.StartActivity("GenerateEmbedding");
    activity?.SetTag("text.length", text.Length);
    
    // Use memory pool for large arrays
    var buffer = ArrayPool<float>.Shared.Rent(1536); // OpenAI embedding size
    try
    {
        var embedding = await _embeddingService.GenerateAsync(text, cancellationToken);
        embedding.CopyTo(buffer.AsSpan(0, embedding.Length));
        
        // Return only the used portion
        return buffer.AsSpan(0, embedding.Length).ToArray();
    }
    finally
    {
        ArrayPool<float>.Shared.Return(buffer);
    }
}
```

## Migration Checklist

### Phase 1: Core Infrastructure
- [ ] Set up .NET project structure
- [ ] Configure dependency injection
- [ ] Implement configuration system
- [ ] Set up logging and monitoring
- [ ] Create base interfaces and abstractions

### Phase 2: Data Layer
- [ ] Design entity models
- [ ] Implement Entity Framework context
- [ ] Create repository patterns
- [ ] Set up database migrations
- [ ] Implement connection management

### Phase 3: Business Logic
- [ ] Migrate training functionality
- [ ] Implement query generation
- [ ] Port prompt engineering logic
- [ ] Add SQL validation and parsing
- [ ] Implement result processing

### Phase 4: External Integrations
- [ ] Migrate LLM service integrations
- [ ] Implement vector store clients
- [ ] Port database provider abstractions
- [ ] Add embedding service implementations
- [ ] Implement caching layer

### Phase 5: API Layer
- [ ] Create REST API controllers
- [ ] Implement request/response models
- [ ] Add authentication and authorization
- [ ] Implement error handling middleware
- [ ] Add API documentation (Swagger)

### Phase 6: Testing and Validation
- [ ] Port unit tests
- [ ] Create integration tests
- [ ] Add performance tests
- [ ] Implement end-to-end tests
- [ ] Validate feature parity

This migration guide provides a comprehensive roadmap for converting the Python Vanna AI project to a modern .NET Core application while maintaining functionality and improving upon the original architecture with .NET-specific best practices.
