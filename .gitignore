# Prerequisites
*.d

# Object files
*.o
*.ko
*.obj
*.elf

# Linker output
*.ilk
*.map
*.exp

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf

# Ignore all obj directories (and their contents) recursively
obj/

# Ignore all Debug build output
Debug/

# Ignore all files with these extensions or names, anywhere in the repo
*.cache
*.sourcelink.json
*.AssemblyInfo.cs
*.AssemblyInfoInputs.cache
*.csproj.AssemblyReference.cache

# DotNetCoreAPI - Clean .gitignore

## Build results
bin/
obj/
out/

## Visual Studio files
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates
.vs/

## Visual Studio Code
.vscode/

## .NET Core
*.dll
*.exe
*.pdb
*.deps.json
*.runtimeconfig.json
*.runtimeconfig.dev.json

## NuGet
*.nupkg
*.snupkg
packages/
.nuget/

## Test results
TestResults/
[Tt]est[Rr]esult*/
*.trx
*.coverage
*.coveragexml

## Logs
*.log
logs/

## Makefile generated log files
api.log
web.log

## Database
*.db
*.sqlite
*.sqlite3

## Environment files
.env
.env.local
.env.development
.env.test
.env.production

## IDE files
*.swp
*.swo
*~

## OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

## Temporary files
*.tmp
*.temp

## JetBrains Rider
.idea/

## Entity Framework
Migrations/

## Application specific
appsettings.local.json
appsettings.*.local.json

## Node.js (if using npm for frontend tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

## Webpack
dist/
build/

## Coverage reports
coverage/
*.lcov

## Dependency directories
jspm_packages/

## Optional npm cache directory
.npm

## Optional REPL history
.node_repl_history

## Output of 'npm pack'
*.tgz

## Yarn Integrity file
.yarn-integrity

## dotenv environment variables file
.env

## Stores VSCode versions used for testing VSCode extensions
.vscode-test

## Azure Functions localsettings file
local.settings.json

## Azurite artifacts
__blobstorage__
__queuestorage__
__azurite_db*__.json

## FuGet.org cache
.fuget/
